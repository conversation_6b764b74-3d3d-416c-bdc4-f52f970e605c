import mongoose from 'mongoose';

const PollTimeSchema = new mongoose.Schema({
  jobId: { 
    type: String, 
    required: true 
  },
  currentProgress : {
    type: Number, // current progress in percentage
    required: true,
  },
  status: {
    type: String,
    enum: ['processing', 'evaluating', 'completed', 'error'],
    default: 'processing'
  },
  timestamp: {
    type: Date, 
    default: Date.now 
  }
}, {
  collection: 'PollTime'
});

export default mongoose.model('PollTime', PollTimeSchema);
