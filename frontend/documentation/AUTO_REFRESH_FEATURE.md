# Auto-Refresh Feature for Grading Results

## Overview

The auto-refresh feature eliminates the need for manual refresh and view buttons by automatically loading evaluation results once grading is complete. This provides a seamless user experience where results appear automatically without user intervention.

## How It Works

### 1. SSE (Server-Sent Events) Monitoring
- Each `StudentCard` component uses the `useSSEGradingProgress` hook to monitor real-time grading progress
- The SSE connection provides live updates on grading status and progress percentage

### 2. Automatic Data Refresh
When SSE indicates grading is complete (`status === 'completed'`) but evaluation results are not yet available:

1. **Individual Auto-Refresh**: Each `StudentCard` automatically triggers a data refresh
2. **Visual Feedback**: The card itself shows loading indicators and status updates
3. **Retry Mechanism**: Intelligent retry with exponential backoff if results aren't immediately available

### 3. Self-Contained Design
- All auto-refresh functionality is contained within the `StudentCard` component
- No modifications to header or parent components required
- Visual indicators are built into the card itself for seamless integration

## Implementation Details

### StudentCard Component
```typescript
// Auto-refresh when grading completes but we don't have evaluation results
useEffect(() => {
    if (
        sseState.status === 'completed' && 
        !sheet.evaluationResult && 
        !hasAutoRefreshed && 
        onRefreshData &&
        retryCount < 3 // Limit retries to 3 attempts
    ) {
        // Trigger automatic refresh with exponential backoff
        const delay = retryCount === 0 ? 1000 : Math.min(2000 * Math.pow(2, retryCount - 1), 8000);
        const delayId = setTimeout(autoRefresh, delay);
        return () => clearTimeout(delayId);
    }
}, [sseState.status, sheet.evaluationResult, hasAutoRefreshed, onRefreshData, retryCount]);
```

### Visual Indicators
```typescript
// Check if auto-refresh is happening
const isAutoRefreshing = sseState.status === 'completed' && !sheet.evaluationResult && isRefreshing;

// Auto-refresh indicator overlay within the card
{isAutoRefreshing && (
    <div className="absolute inset-0 bg-primary/5 rounded-lg flex items-center justify-center">
        <div className="flex items-center gap-2 text-primary text-sm font-medium">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
            <span>
                {retryCount > 0 ? `Retrying... (${retryCount}/3)` : 'Loading results...'}
            </span>
        </div>
    </div>
)}
```

## Key Improvements

### 1. Robust State Management
- **Timeout Protection**: 10-second timeout prevents infinite loading states
- **Retry Logic**: Up to 3 retry attempts with exponential backoff
- **State Reset**: Proper cleanup when evaluation results become available

### 2. No Toast Spam
- **Removed Toast Notifications**: No more overwhelming notifications for multiple students
- **Visual Feedback Only**: Status is shown through card overlays and button states
- **Clean User Experience**: Focus on visual indicators within each card

### 3. Intelligent Retry Strategy
- **Exponential Backoff**: Delays increase with each retry (1s, 2s, 4s, 8s max)
- **Retry Counter**: Shows current retry attempt in UI
- **Graceful Fallback**: Falls back to manual refresh after 3 attempts

## Benefits

1. **Improved User Experience**: No need to manually refresh or click buttons
2. **Real-time Updates**: Results appear as soon as they're available
3. **Reduced User Friction**: Eliminates the cognitive load of remembering to refresh
4. **Self-Contained Design**: All functionality within the card component
5. **Visual Feedback**: Users always know what's happening with clear status indicators
6. **Design Preservation**: No modifications to existing header or layout components
7. **No Notification Spam**: Clean interface without overwhelming toast messages

## User Interface Features

### Visual Feedback Within Cards
- **Loading Overlay**: Semi-transparent overlay with spinner when auto-refresh is active
- **Background Highlight**: Subtle background color change to indicate active state
- **Button State Changes**: Dynamic button text showing retry progress
- **Retry Indicators**: Shows current retry attempt (e.g., "Retrying... (2/3)")

### Seamless Integration
- **No Layout Changes**: Existing design remains intact
- **Responsive Design**: Works across all screen sizes
- **Accessibility**: Proper ARIA labels and keyboard navigation
- **Dark Mode Support**: Consistent theming across light and dark modes

## Error Handling

- **Timeout Protection**: 10-second timeout prevents stuck states
- **Retry Logic**: Up to 3 attempts with exponential backoff
- **Fallback**: Manual refresh available after retry attempts exhausted
- **State Recovery**: Proper cleanup and state reset on success/failure
- **Graceful Degradation**: System continues to work even if auto-refresh fails

## Configuration

The auto-refresh feature can be configured through:

- **Retry Attempts**: Currently set to 3 maximum attempts
- **Timeout Duration**: 10 seconds maximum wait time
- **Backoff Strategy**: Exponential backoff with 8-second maximum delay
- **Initial Delay**: 1 second before first refresh attempt

## Future Enhancements

1. **Smart Retry**: More sophisticated retry logic based on error types
2. **Progress Indicators**: Show estimated time remaining for result loading
3. **Offline Support**: Queue refresh attempts when connection is restored
4. **Batch Optimization**: Optimize multiple simultaneous refreshes 