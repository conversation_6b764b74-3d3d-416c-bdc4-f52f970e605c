import React, { useState } from 'react';
import { PDFDocument, rgb } from 'pdf-lib';
import { ViewColumnsIcon } from '@heroicons/react/24/outline';
import { useS3Utils } from '@/hooks/useS3Utils';
import { 
    BoundingBox, 
    ParagraphAnalysis, 
    QuestionFeedback, 
    JsonQuestionEvaluation 
} from '@/types/aegisGrader';

// Type definitions - Updated for new UPSC evaluation structure
interface CriterionBreakdown {
  criterion: string;
  score: string;
  maxScore?: string;
}

interface Paragraph {
  text: string;
  boundingBox: BoundingBox;
}

interface FeedbackSection {
  title: string;
  content: string;
  subsections?: { 
    title: string; 
    content: string; 
    bounding_box?: BoundingBox;
    subsections?: { title: string; content: string; bounding_box?: BoundingBox }[];
  }[];
}

interface QuestionBreakdown {
  questionNumber: string;
  marksAwarded: number;
  marksPossible: number;
  percentage: number;
  feedback: string;
  structuredFeedback?: FeedbackSection[];
  criteriaBreakdown: CriterionBreakdown[];
  paragraphs?: Paragraph[];
}

interface EvaluationBreakdown {
  totalMarks: number;
  maxMarks: number;
  overallPercentage: number;
  questions: QuestionBreakdown[];
}

interface SubmissionData {
  studentName: string;
  rollNumber: string;
  totalMarks: number;
  maxMarks: number;
  percentage: number;
  pdfUrl?: string;
  detailedBreakdown: EvaluationBreakdown;
  subjectName?: string;
}

interface DownloadStudentReportProps {
  submissionData: SubmissionData;
  breakdownContentRef: React.RefObject<HTMLDivElement>;
}

const DownloadStudentReport: React.FC<DownloadStudentReportProps> = ({ submissionData, breakdownContentRef }) => {
  const [isDownloadingSideBySide, setIsDownloadingSideBySide] = useState(false);
  const { fetchPdfAsBlob } = useS3Utils();

  // Theme colors for PDF (light, professional colors)
  const themeColors = {
    primary: rgb(0.08, 0.12, 0.32), // Deep blue from your theme
    secondary: rgb(0.2, 0.3, 0.5), // Medium blue
    accent: rgb(0.0, 0.5, 1.0), // Bright blue accent
    muted: rgb(0.4, 0.4, 0.5), // Muted text
    lightMuted: rgb(0.6, 0.6, 0.7), // Light muted text
    success: rgb(0.2, 0.6, 0.3), // Green for good scores
    warning: rgb(0.8, 0.6, 0.2), // Orange for average scores
    danger: rgb(0.8, 0.3, 0.3), // Red for poor scores
    background: rgb(1, 1, 1), // White background
    border: rgb(0.9, 0.9, 0.95), // Light border
  };

  const downloadSideBySideReport = async () => {
    console.log('Downloading side-by-side report...');
    setIsDownloadingSideBySide(true);

    try {
      if (submissionData.pdfUrl) {
        await createAnnotatedAnswerSheet();
      } else {
        alert('No answer sheet PDF available for side-by-side view.');
      }
      console.log('Side-by-side report downloaded successfully');
    } catch (error) {
      console.error('Error generating side-by-side report:', error);
      alert('Failed to generate side-by-side report. Please try again.');
    } finally {
      setIsDownloadingSideBySide(false);
    }
  };

  const createAnnotatedAnswerSheet = async () => {
    try {
      console.log('Creating side-by-side answer sheet...');
      
      if (!submissionData.pdfUrl) {
        console.log('No student answer PDF URL available');
        return;
      }

      const evaluationData = submissionData.detailedBreakdown;
      console.log('Evaluation data:', evaluationData);
      
      if (!evaluationData || !evaluationData.questions) {
        console.log('No evaluation data available for annotations');
        return;
      }

      console.log('Fetching student answer PDF from S3:', submissionData.pdfUrl);
      const pdfBlob = await fetchPdfAsBlob(submissionData.pdfUrl);
      console.log('PDF blob received, size:', pdfBlob.size);
      
      const pdfBytes = await pdfBlob.arrayBuffer();
      const pdfHeader = new Uint8Array(pdfBytes.slice(0, 4));
      const pdfSignature = new TextDecoder().decode(pdfHeader);
      
      if (pdfSignature !== '%PDF') {
        throw new Error(`Invalid PDF file. File header: ${pdfSignature}. Expected: %PDF`);
      }
      
      const originalPdfDoc = await PDFDocument.load(pdfBytes);
      const originalPages = originalPdfDoc.getPages();
      
      const newPdfDoc = await PDFDocument.create();
      
      // Try to embed logo
      let logoImage = null;
      try {
        const logoResponse = await fetch('/logo_accent.png');
        if (logoResponse.ok) {
          const logoArrayBuffer = await logoResponse.arrayBuffer();
          logoImage = await newPdfDoc.embedPng(logoArrayBuffer);
        }
      } catch (logoError) {
        console.log('Could not load logo, proceeding without it:', logoError);
      }

      // Process each page of the original PDF
      for (let pageIndex = 0; pageIndex < originalPages.length; pageIndex++) {
        const originalPage = originalPages[pageIndex];
        const pageNumber = pageIndex + 1;
        
        // Create a new page with landscape orientation for side-by-side layout
        const newPage = newPdfDoc.addPage([1190.56, 841.89]); // A4 landscape
        const { width: pageWidth, height: pageHeight } = newPage.getSize();
        const margin = 50; // Define margin for this function
        
        // Improved helper function to draw text with word wrapping and better spacing
        const drawWrappedText = (text: string, x: number, y: number, maxWidth: number, fontSize: number, page: any, color = themeColors.primary) => {
          if (!text || typeof text !== 'string') return y;
          
          const cleanText = text
            .replace(/[\x00-\x1F\x7F]/g, '')
            .replace(/[""]/g, '"')
            .replace(/['']/g, "'")
            .replace(/\s+/g, ' ')
            .trim();
          
          if (!cleanText) return y;
          
          // Ensure maxWidth doesn't exceed page boundaries with safety margin
          const actualMaxWidth = Math.min(maxWidth, pageWidth - margin - x - 20);
          
          const words = cleanText.split(' ');
          let line = '';
          const charWidth = fontSize * 0.6; // More accurate character width
          const lineHeight = fontSize * 1.4; // Increased line height for better readability
          let currentY = y;
          
          for (const word of words) {
            const testLine = line + (line ? ' ' : '') + word;
            const textWidth = testLine.length * charWidth;
            
            if (textWidth > actualMaxWidth && line) {
              // Draw the current line and wrap to next line
              page.drawText(line, { x, y: currentY, size: fontSize, color });
              currentY -= lineHeight;
              line = word;
            } else {
              line = testLine;
            }
          }
          
          if (line) {
            // Draw the last line
            page.drawText(line, { x, y: currentY, size: fontSize, color });
            currentY -= lineHeight;
          }
          
          return currentY;
        };
        
        // Add company header with consistent spacing
        const headerY = pageHeight - 60;
        if (logoImage) {
          // Draw logo
          const logoWidth = 30;
          const logoHeight = 30;
          newPage.drawImage(logoImage, {
            x: margin,
            y: headerY - logoHeight + 10,
            width: logoWidth,
            height: logoHeight,
          });
          // Draw company name next to logo
          newPage.drawText('AegisScholar', {
            x: margin + logoWidth + 15,
            y: headerY,
            size: 14,
            color: themeColors.primary
          });
        } else {
          newPage.drawText('AegisScholar', {
            x: margin,
            y: headerY,
            size: 14,
            color: themeColors.primary
          });
        }
        
        // Add subtitle with consistent spacing
        const subtitleY = headerY - 25;
        newPage.drawText('Annotated Answer Sheet', {
          x: logoImage ? margin + 30 + 15 : margin,
          y: subtitleY,
          size: 10,
          color: themeColors.secondary
        });
        
        // Add student info with better positioning and truncation
        const studentInfoX = Math.max(margin + 400, pageWidth - 350); // Ensure it doesn't overlap with logo
        const studentInfoY = headerY;
        const studentInfoText = `${submissionData.studentName} | ${submissionData.rollNumber}`;
        const studentInfoMaxWidth = pageWidth - studentInfoX - margin;
        const studentInfoDisplayText = studentInfoText.length * 10 * 0.6 > studentInfoMaxWidth ? 
          studentInfoText.substring(0, Math.floor(studentInfoMaxWidth / (10 * 0.6)) - 3) + '...' : 
          studentInfoText;
        
        newPage.drawText(studentInfoDisplayText, {
          x: studentInfoX,
          y: studentInfoY,
          size: 10,
          color: themeColors.muted
        });
        
        // Add score with consistent spacing
        const scoreY = subtitleY;
        const scoreText = `Score: ${submissionData.totalMarks}/${submissionData.maxMarks} (${submissionData.percentage}%)`;
        const scoreDisplayText = scoreText.length * 10 * 0.6 > studentInfoMaxWidth ? 
          scoreText.substring(0, Math.floor(studentInfoMaxWidth / (10 * 0.6)) - 3) + '...' : 
          scoreText;
        
        newPage.drawText(scoreDisplayText, {
          x: studentInfoX,
          y: scoreY,
          size: 10,
          color: submissionData.percentage >= 80 ? themeColors.success : 
                 submissionData.percentage >= 60 ? themeColors.warning : themeColors.danger
        });
        
        // Embed the original page on the left side with better positioning
        const embeddedPage = await newPdfDoc.embedPage(originalPage);
        const scale = Math.min(400 / embeddedPage.width, 700 / embeddedPage.height); // Reduced height to prevent overlap
        const embeddedWidth = embeddedPage.width * scale;
        const embeddedHeight = embeddedPage.height * scale;
        
        // Position embedded page with consistent margins
        const embeddedX = margin;
        const embeddedY = pageHeight - embeddedHeight - 120; // Increased top margin
        
        newPage.drawPage(embeddedPage, {
          x: embeddedX,
          y: embeddedY,
          width: embeddedWidth,
          height: embeddedHeight
        });
        
        // Add page number label with consistent spacing
        newPage.drawText(`Page ${pageNumber}`, {
          x: embeddedX,
          y: embeddedY - 15,
          size: 10,
          color: themeColors.muted
        });
        
        // Add feedback content on the right side with improved layout
        const feedbackX = Math.max(embeddedWidth + 120, margin + 500); // Increased spacing from embedded page
        const feedbackY = pageHeight - 120; // Consistent with embedded page positioning
        const feedbackWidth = pageWidth - feedbackX - margin; // Ensure it doesn't exceed right margin
        
        // Find questions that have feedback for this page
        const questionsForThisPage = evaluationData.questions.filter((question: QuestionBreakdown) => {
          if (question.structuredFeedback && question.structuredFeedback.length > 0) {
            return question.structuredFeedback.some(section => 
              section.subsections && section.subsections.some(subsection => 
                subsection.bounding_box && 
                subsection.bounding_box.page === pageNumber
              )
            );
          }
          if (question.paragraphs && question.paragraphs.length > 0) {
            return question.paragraphs.some(paragraph => 
              paragraph.boundingBox.page === pageNumber
            );
          }
          return false;
        });

        // Helper function to split long text into manageable chunks
        const splitTextIntoChunks = (text: string, maxChunkLength: number = 200): string[] => {
          if (!text || text.length <= maxChunkLength) {
            return [text];
          }
          
          const chunks: string[] = [];
          let currentChunk = '';
          const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
          
          for (const sentence of sentences) {
            const trimmedSentence = sentence.trim();
            if (trimmedSentence.length === 0) continue;
            
            if ((currentChunk + ' ' + trimmedSentence).length <= maxChunkLength) {
              currentChunk += (currentChunk ? ' ' : '') + trimmedSentence + '.';
            } else {
              if (currentChunk) {
                chunks.push(currentChunk.trim());
              }
              currentChunk = trimmedSentence + '.';
            }
          }
          
          if (currentChunk) {
            chunks.push(currentChunk.trim());
          }
          
          return chunks.length > 0 ? chunks : [text];
        };

        // Helper function to render structured feedback in a readable format
        const renderStructuredFeedback = (feedback: FeedbackSection[], page: any, startX: number, startY: number, maxWidth: number, pageDoc: any, pageMargin: number = 50): number => {
          let currentY = startY;
          
          feedback.forEach((section) => {
            if (section.subsections && section.subsections.length > 0) {
              section.subsections.forEach((subsection) => {
                // Check if we need a new page
                if (currentY < pageMargin + 150) {
                  const newPage = pageDoc.addPage([1190.56, 841.89]);
                  const { width: newPageWidth, height: newPageHeight } = newPage.getSize();
                  
                  // Add header to new page
                  newPage.drawText('AegisScholar', {
                    x: pageMargin,
                    y: newPageHeight - 40,
                    size: 12,
                    color: themeColors.primary
                  });
                  
                  newPage.drawText('Question Feedback (continued)', {
                    x: pageMargin,
                    y: newPageHeight - 55,
                    size: 10,
                    color: themeColors.secondary
                  });
                  
                  // Reset currentY for new page
                  currentY = newPageHeight - 100;
                  
                  // Helper function for new page
                  const drawWrappedTextOnNewPage = (text: string, x: number, y: number, maxWidth: number, fontSize: number, color = themeColors.primary) => {
                    if (!text || typeof text !== 'string') return y;
                    
                    const cleanText = text
                      .replace(/[\x00-\x1F\x7F]/g, '')
                      .replace(/[""]/g, '"')
                      .replace(/['']/g, "'")
                      .replace(/\s+/g, ' ')
                      .trim();
                    
                    if (!cleanText) return y;
                    
                    const actualMaxWidth = Math.min(maxWidth, newPageWidth - pageMargin - x - 20);
                    const words = text.split(' ');
                    let line = '';
                    const charWidth = fontSize * 0.6;
                    const lineHeight = fontSize * 1.4;
                    let currentY = y;
                    
                    for (const word of words) {
                      const testLine = line + (line ? ' ' : '') + word;
                      const textWidth = testLine.length * charWidth;
                      
                      if (textWidth > actualMaxWidth && line) {
                        newPage.drawText(line, { x, y: currentY, size: fontSize, color });
                        currentY -= lineHeight;
                        line = word;
                      } else {
                        line = testLine;
                      }
                    }
                    
                    if (line) {
                      newPage.drawText(line, { x, y: currentY, size: fontSize, color });
                      currentY -= lineHeight;
                    }
                    
                    return currentY;
                  };
                  
                  // Draw subsection title
                  newPage.drawText(`• ${subsection.title}`, {
                    x: pageMargin + 10,
                    y: currentY,
                    size: 9,
                    color: themeColors.muted
                  });
                  currentY -= 20;
                  
                  // Draw content in chunks
                  if (subsection.content) {
                    const contentChunks = splitTextIntoChunks(subsection.content, 150);
                    contentChunks.forEach((chunk, chunkIndex) => {
                      currentY = drawWrappedTextOnNewPage(chunk, pageMargin + 10, currentY, newPageWidth - pageMargin - 20, 9, themeColors.lightMuted);
                      if (chunkIndex < contentChunks.length - 1) {
                        currentY -= 10;
                      } else {
                        currentY -= 15;
                      }
                    });
                  }
                  
                  return currentY;
                }
                
                // Draw subsection title
                page.drawText(`• ${subsection.title}`, {
                  x: startX + 20,
                  y: currentY,
                  size: 9,
                  color: themeColors.muted
                });
                currentY -= 20;
                
                // Draw content in chunks
                if (subsection.content) {
                  const contentChunks = splitTextIntoChunks(subsection.content, 150);
                  contentChunks.forEach((chunk, chunkIndex) => {
                    currentY = drawWrappedText(chunk, startX + 20, currentY, maxWidth - 20, 9, page, themeColors.lightMuted);
                    if (chunkIndex < contentChunks.length - 1) {
                      currentY -= 10;
                    } else {
                      currentY -= 15;
                    }
                  });
                }
              });
            }
          });
          
          return currentY;
        };
         
        if (questionsForThisPage.length > 0) {
          // Add feedback header with consistent spacing
          newPage.drawText('Question Feedback:', {
            x: feedbackX,
            y: feedbackY,
            size: 14,
            color: themeColors.primary
          });
          
          let currentY = feedbackY - 35; // Increased spacing after header
          
          questionsForThisPage.forEach((question: QuestionBreakdown) => {
            // Check if we need a new page before starting a new question
            if (currentY < margin + 150) { // Increased safety margin
              // Create a new page for this content
              const newPageForContent = newPdfDoc.addPage([1190.56, 841.89]);
              const { width: newPageWidth, height: newPageHeight } = newPageForContent.getSize();
              const newMargin = 50;
              
              // Add header to new page with consistent spacing
              newPageForContent.drawText('AegisScholar', {
                x: newMargin,
                y: newPageHeight - 40,
                size: 12,
                color: themeColors.primary
              });
              
              newPageForContent.drawText('Question Feedback (continued)', {
                x: newMargin,
                y: newPageHeight - 55,
                size: 10,
                color: themeColors.secondary
              });
              
              // Reset currentY for new page with consistent spacing
              currentY = newPageHeight - 100;
              
              // Helper function for new page with improved spacing
              const drawWrappedTextOnNewPage = (text: string, x: number, y: number, maxWidth: number, fontSize: number, color = themeColors.primary) => {
                if (!text || typeof text !== 'string') return y;
                
                const cleanText = text
                  .replace(/[\x00-\x1F\x7F]/g, '')
                  .replace(/[""]/g, '"')
                  .replace(/['']/g, "'")
                  .replace(/\s+/g, ' ')
                  .trim();
                
                if (!cleanText) return y;
                
                const actualMaxWidth = Math.min(maxWidth, newPageWidth - newMargin - x - 20);
                const words = cleanText.split(' ');
                let line = '';
                const charWidth = fontSize * 0.6;
                const lineHeight = fontSize * 1.4;
                let currentY = y;
                
                for (const word of words) {
                  const testLine = line + (line ? ' ' : '') + word;
                  const textWidth = testLine.length * charWidth;
                  
                  if (textWidth > actualMaxWidth && line) {
                    newPageForContent.drawText(line, { x, y: currentY, size: fontSize, color });
                    currentY -= lineHeight;
                    line = word;
                  } else {
                    line = testLine;
                  }
                }
                
                if (line) {
                  newPageForContent.drawText(line, { x, y: currentY, size: fontSize, color });
                  currentY -= lineHeight;
                }
                
                return currentY;
              };
              
              // Continue with the current question on the new page
              // Add question header
              newPageForContent.drawText(`Question ${question.questionNumber}:`, {
                x: newMargin,
                y: currentY,
                size: 12,
                color: themeColors.primary
              });
              currentY -= 25;
              
              // Add marks
              const questionScoreColor = question.percentage >= 80 ? themeColors.success : 
                                       question.percentage >= 60 ? themeColors.warning : themeColors.danger;
              newPageForContent.drawText(`Marks: ${question.marksAwarded}/${question.marksPossible}`, {
                x: newMargin + 20,
                y: currentY,
                size: 10,
                color: questionScoreColor
              });
              currentY -= 25;
              
              // Continue with feedback content on new page using the new structured approach
              if (question.structuredFeedback && question.structuredFeedback.length > 0) {
                // Filter feedback for this specific page
                const pageFeedback = question.structuredFeedback.map(section => ({
                  ...section,
                  subsections: section.subsections?.filter(subsection => 
                    subsection.bounding_box && subsection.bounding_box.page === pageNumber
                  ) || []
                })).filter(section => section.subsections.length > 0);
                
                if (pageFeedback.length > 0) {
                  currentY = renderStructuredFeedback(pageFeedback, newPageForContent, newMargin + 10, currentY, newPageWidth - newMargin - 20, newPdfDoc, newMargin);
                }
              }
              
              return; // Skip the original drawing since we handled it on the new page
            }
            
            // Get question page context for display
            const questionPages = new Set<number>();
            if (question.structuredFeedback && question.structuredFeedback.length > 0) {
              question.structuredFeedback.forEach(section => {
                section.subsections?.forEach(subsection => {
                  if (subsection.bounding_box) {
                    questionPages.add(subsection.bounding_box.page);
                  }
                });
              });
            }
            if (question.paragraphs && question.paragraphs.length > 0) {
              question.paragraphs.forEach(paragraph => {
                questionPages.add(paragraph.boundingBox.page);
              });
            }
             
            // Show page span if question spans multiple pages
            const pageContext = questionPages.size > 1 ? 
              ` (spans pages ${Array.from(questionPages).sort((a, b) => a - b).join(', ')})` : '';
             
            // Add question header with consistent spacing
            newPage.drawText(`Question ${question.questionNumber}${pageContext}:`, {
              x: feedbackX,
              y: currentY,
              size: 12,
              color: themeColors.primary
            });
            currentY -= 25; // Increased spacing
            
            // Add marks with consistent spacing
            const questionScoreColor = question.percentage >= 80 ? themeColors.success : 
                                     question.percentage >= 60 ? themeColors.warning : themeColors.danger;
            newPage.drawText(`Marks: ${question.marksAwarded}/${question.marksPossible}`, {
              x: feedbackX + 20,
              y: currentY,
              size: 10,
              color: questionScoreColor
            });
            currentY -= 25; // Increased spacing
            
            // Add feedback content using the new structured approach
            if (question.structuredFeedback && question.structuredFeedback.length > 0) {
              // Filter feedback for this specific page
              const pageFeedback = question.structuredFeedback.map(section => ({
                ...section,
                subsections: section.subsections?.filter(subsection => 
                  subsection.bounding_box && subsection.bounding_box.page === pageNumber
                ) || []
              })).filter(section => section.subsections.length > 0);
              
              if (pageFeedback.length > 0) {
                currentY = renderStructuredFeedback(pageFeedback, newPage, feedbackX, currentY, feedbackWidth, newPdfDoc, margin);
              }
            } else if (question.feedback) {
              // Use regular feedback as fallback with consistent spacing
              currentY = drawWrappedText(question.feedback, feedbackX + 20, currentY, feedbackWidth - 20, 10, newPage, themeColors.muted);
              currentY -= 20;
            }
            
            currentY -= 20; // Extra spacing between questions
          });
        } else {
          // No specific feedback for this page - show a subtle indicator
          newPage.drawText('No feedback for this page', {
            x: feedbackX,
            y: feedbackY - 50,
            size: 10,
            color: themeColors.lightMuted
          });
        }
        
        // Add footer with consistent positioning
        newPage.drawText('Generated by AegisScholar', {
          x: margin,
          y: 40,
          size: 8,
          color: themeColors.muted
        });
        
        newPage.drawText('AI-Powered Educational Assessment Platform', {
          x: margin,
          y: 30,
          size: 7,
          color: themeColors.lightMuted
        });
      }
      
      // Save and download the side-by-side PDF
      const sideBySidePdfBytes = await newPdfDoc.save();
      const blob = new Blob([sideBySidePdfBytes], { type: 'application/pdf' });

      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${submissionData.studentName.replace(/\s+/g, '_')}_Annotated_Answer_Sheet_${new Date().toISOString().split('T')[0]}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      console.log('Side-by-side answer sheet with feedback created and downloaded successfully');

    } catch (error) {
      console.error('Error creating side-by-side answer sheet:', error);
      throw error;
    }
  };

  return (
    <div className="download-report-container space-y-3">
      <div className="flex flex-row gap-2">
        {submissionData.pdfUrl && (
          <button
            onClick={downloadSideBySideReport}
            disabled={isDownloadingSideBySide}
            className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-foreground bg-muted hover:bg-accent/50 border border-border rounded-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            title="Download Annotated Answer Sheet"
          >
            <ViewColumnsIcon className="w-4 h-4" />
            <span className="hidden sm:inline">
              {isDownloadingSideBySide ? 'Generating...' : 'Download Annotated Answer Sheet'}
            </span>
            <span className="sm:hidden">
              {isDownloadingSideBySide ? 'Generating...' : 'Annotated'}
            </span>
          </button>
        )}
      </div>
    </div>
  );
};

export default DownloadStudentReport; 