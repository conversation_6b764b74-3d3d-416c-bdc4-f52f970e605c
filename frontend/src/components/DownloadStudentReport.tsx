import React, { useState } from 'react';
import { PDFDocument, rgb } from 'pdf-lib';
import { ViewColumnsIcon } from '@heroicons/react/24/outline';
import { useS3Utils } from '@/hooks/useS3Utils';
import MarkdownRenderer from './MarkdownRenderer';
import { markdownToPlainText } from '@/utils/markdownToPdf';
import {
    BoundingBox,
    ParagraphAnalysis,
    QuestionFeedback,
    JsonQuestionEvaluation
} from '@/types/aegisGrader';

// Type definitions - Updated for new UPSC evaluation structure
interface CriterionBreakdown {
  criterion: string;
  score: string;
  maxScore?: string;
}

interface Paragraph {
  text: string;
  boundingBox: BoundingBox;
}

interface FeedbackSection {
  title: string;
  content: string;
  subsections?: { 
    title: string; 
    content: string; 
    bounding_box?: BoundingBox;
    subsections?: { title: string; content: string; bounding_box?: BoundingBox }[];
  }[];
}

interface QuestionBreakdown {
  questionNumber: string;
  marksAwarded: number;
  marksPossible: number;
  percentage: number;
  feedback: string;
  structuredFeedback?: FeedbackSection[];
  criteriaBreakdown: CriterionBreakdown[];
  paragraphs?: Paragraph[];
}

interface EvaluationBreakdown {
  totalMarks: number;
  maxMarks: number;
  overallPercentage: number;
  questions: QuestionBreakdown[];
}

interface SubmissionData {
  studentName: string;
  rollNumber: string;
  totalMarks: number;
  maxMarks: number;
  percentage: number;
  pdfUrl?: string;
  detailedBreakdown: EvaluationBreakdown;
  subjectName?: string;
}

interface DownloadStudentReportProps {
  submissionData: SubmissionData;
  breakdownContentRef: React.RefObject<HTMLDivElement>;
}

const DownloadStudentReport: React.FC<DownloadStudentReportProps> = ({ submissionData, breakdownContentRef }) => {
  const [isDownloadingSideBySide, setIsDownloadingSideBySide] = useState(false);
  const { fetchPdfAsBlob } = useS3Utils();



  // Theme colors for PDF (light, professional colors)
  const themeColors = {
    primary: rgb(0.08, 0.12, 0.32), // Deep blue from your theme
    secondary: rgb(0.2, 0.3, 0.5), // Medium blue
    accent: rgb(0.0, 0.5, 1.0), // Bright blue accent
    muted: rgb(0.4, 0.4, 0.5), // Muted text
    lightMuted: rgb(0.6, 0.6, 0.7), // Light muted text
    success: rgb(0.2, 0.6, 0.3), // Green for good scores
    warning: rgb(0.8, 0.6, 0.2), // Orange for average scores
    danger: rgb(0.8, 0.3, 0.3), // Red for poor scores
    background: rgb(1, 1, 1), // White background
    border: rgb(0.9, 0.9, 0.95), // Light border
  };

  // Deterministic text drawing configuration
  const textConfig = {
    fontSize: {
      header: 14,
      subheader: 12,
      body: 10,
      small: 9,
      tiny: 8
    },
    lineHeight: {
      header: 20,
      subheader: 18,
      body: 14,
      small: 13,
      tiny: 11
    },
    spacing: {
      section: 25,
      paragraph: 15,
      line: 5
    }
  };

  const downloadSideBySideReport = async () => {
    console.log('Downloading side-by-side report...');
    setIsDownloadingSideBySide(true);

    try {
      if (submissionData.pdfUrl) {
        await createAnnotatedAnswerSheet();
      } else {
        alert('No answer sheet PDF available for side-by-side view.');
      }
      console.log('Side-by-side report downloaded successfully');
    } catch (error) {
      console.error('Error generating side-by-side report:', error);
      alert('Failed to generate side-by-side report. Please try again.');
    } finally {
      setIsDownloadingSideBySide(false);
    }
  };

  const createAnnotatedAnswerSheet = async () => {
    try {
      console.log('Creating side-by-side answer sheet...');
      
      if (!submissionData.pdfUrl) {
        console.log('No student answer PDF URL available');
        return;
      }

      const evaluationData = submissionData.detailedBreakdown;
      console.log('Evaluation data:', evaluationData);
      
      if (!evaluationData || !evaluationData.questions) {
        console.log('No evaluation data available for annotations');
        return;
      }

      console.log('Fetching student answer PDF from S3:', submissionData.pdfUrl);
      const pdfBlob = await fetchPdfAsBlob(submissionData.pdfUrl);
      console.log('PDF blob received, size:', pdfBlob.size);
      
      const pdfBytes = await pdfBlob.arrayBuffer();
      const pdfHeader = new Uint8Array(pdfBytes.slice(0, 4));
      const pdfSignature = new TextDecoder().decode(pdfHeader);
      
      if (pdfSignature !== '%PDF') {
        throw new Error(`Invalid PDF file. File header: ${pdfSignature}. Expected: %PDF`);
      }
      
      const originalPdfDoc = await PDFDocument.load(pdfBytes);
      const originalPages = originalPdfDoc.getPages();
      
      const newPdfDoc = await PDFDocument.create();
      
      // Try to embed logo
      let logoImage = null;
      try {
        const logoResponse = await fetch('/logo_accent.png');
        if (logoResponse.ok) {
          const logoArrayBuffer = await logoResponse.arrayBuffer();
          logoImage = await newPdfDoc.embedPng(logoArrayBuffer);
        }
      } catch (logoError) {
        console.log('Could not load logo, proceeding without it:', logoError);
      }

      // Process each page of the original PDF
      for (let pageIndex = 0; pageIndex < originalPages.length; pageIndex++) {
        const originalPage = originalPages[pageIndex];
        const pageNumber = pageIndex + 1;
        
        // Create a new page with landscape orientation for side-by-side layout
        const newPage = newPdfDoc.addPage([1190.56, 841.89]); // A4 landscape
        const { width: pageWidth, height: pageHeight } = newPage.getSize();
        const margin = 50; // Define margin for this function
        
        // Deterministic text drawing function with consistent spacing
        const drawText = (
          text: string,
          x: number,
          y: number,
          maxWidth: number,
          fontSize: number,
          page: any,
          color = themeColors.primary,
          options: {
            isBold?: boolean,
            isTitle?: boolean,
            maxLines?: number
          } = {}
        ): number => {
          if (!text || typeof text !== 'string') return y;

          // Clean and prepare text
          const cleanText = markdownToPlainText(text);
          if (!cleanText) return y;

          // Calculate dimensions
          const charWidth = fontSize * 0.55; // More precise character width
          const lineHeight = textConfig.lineHeight[fontSize === textConfig.fontSize.header ? 'header' :
                                                   fontSize === textConfig.fontSize.subheader ? 'subheader' :
                                                   fontSize === textConfig.fontSize.body ? 'body' :
                                                   fontSize === textConfig.fontSize.small ? 'small' : 'tiny'];

          // Ensure we don't exceed boundaries
          const safeMaxWidth = Math.min(maxWidth, pageWidth - x - margin);
          const maxCharsPerLine = Math.floor(safeMaxWidth / charWidth);

          // Split text into lines
          const words = cleanText.split(' ');
          const lines: string[] = [];
          let currentLine = '';

          for (const word of words) {
            const testLine = currentLine + (currentLine ? ' ' : '') + word;
            if (testLine.length <= maxCharsPerLine) {
              currentLine = testLine;
            } else {
              if (currentLine) {
                lines.push(currentLine);
                currentLine = word;
              } else {
                // Word is too long, truncate it
                lines.push(word.substring(0, maxCharsPerLine - 3) + '...');
                currentLine = '';
              }
            }
          }
          if (currentLine) {
            lines.push(currentLine);
          }

          // Apply max lines limit if specified (but prefer showing full content)
          const finalLines = options.maxLines && options.maxLines > 0 ? lines.slice(0, options.maxLines) : lines;
          if (options.maxLines && options.maxLines > 0 && lines.length > options.maxLines) {
            finalLines[finalLines.length - 1] = finalLines[finalLines.length - 1].substring(0, maxCharsPerLine - 3) + '...';
          }

          // Draw lines
          let currentY = y;
          finalLines.forEach((line) => {
            page.drawText(line, {
              x,
              y: currentY,
              size: fontSize,
              color
            });
            currentY -= lineHeight;
          });

          return currentY - (options.isTitle ? textConfig.spacing.section : textConfig.spacing.paragraph);
        };
        
        // Add company header with consistent spacing
        const headerY = pageHeight - 60;
        if (logoImage) {
          // Draw logo
          const logoWidth = 30;
          const logoHeight = 30;
          newPage.drawImage(logoImage, {
            x: margin,
            y: headerY - logoHeight + 10,
            width: logoWidth,
            height: logoHeight,
          });
          // Draw company name next to logo
          newPage.drawText('AegisScholar', {
            x: margin + logoWidth + 15,
            y: headerY,
            size: 14,
            color: themeColors.primary
          });
        } else {
          newPage.drawText('AegisScholar', {
            x: margin,
            y: headerY,
            size: 14,
            color: themeColors.primary
          });
        }
        
        // Add subtitle with consistent spacing
        const subtitleY = headerY - 25;
        newPage.drawText('Annotated Answer Sheet', {
          x: logoImage ? margin + 30 + 15 : margin,
          y: subtitleY,
          size: 10,
          color: themeColors.secondary
        });
        
        // Add student info with better positioning and truncation
        const studentInfoX = Math.max(margin + 400, pageWidth - 350); // Ensure it doesn't overlap with logo
        const studentInfoY = headerY;
        const studentInfoText = `${submissionData.studentName} | ${submissionData.rollNumber}`;
        const studentInfoMaxWidth = pageWidth - studentInfoX - margin;
        const studentInfoDisplayText = studentInfoText.length * 10 * 0.6 > studentInfoMaxWidth ? 
          studentInfoText.substring(0, Math.floor(studentInfoMaxWidth / (10 * 0.6)) - 3) + '...' : 
          studentInfoText;
        
        newPage.drawText(studentInfoDisplayText, {
          x: studentInfoX,
          y: studentInfoY,
          size: 10,
          color: themeColors.muted
        });
        
        // Add score with consistent spacing
        const scoreY = subtitleY;
        const scoreText = `Score: ${submissionData.totalMarks}/${submissionData.maxMarks} (${submissionData.percentage}%)`;
        const scoreDisplayText = scoreText.length * 10 * 0.6 > studentInfoMaxWidth ? 
          scoreText.substring(0, Math.floor(studentInfoMaxWidth / (10 * 0.6)) - 3) + '...' : 
          scoreText;
        
        newPage.drawText(scoreDisplayText, {
          x: studentInfoX,
          y: scoreY,
          size: 10,
          color: submissionData.percentage >= 80 ? themeColors.success : 
                 submissionData.percentage >= 60 ? themeColors.warning : themeColors.danger
        });
        
        // Embed the original page on the left side with better positioning
        const embeddedPage = await newPdfDoc.embedPage(originalPage);
        const scale = Math.min(400 / embeddedPage.width, 700 / embeddedPage.height); // Reduced height to prevent overlap
        const embeddedWidth = embeddedPage.width * scale;
        const embeddedHeight = embeddedPage.height * scale;
        
        // Position embedded page with consistent margins
        const embeddedX = margin;
        const embeddedY = pageHeight - embeddedHeight - 120; // Increased top margin
        
        newPage.drawPage(embeddedPage, {
          x: embeddedX,
          y: embeddedY,
          width: embeddedWidth,
          height: embeddedHeight
        });
        
        // Add page number label with consistent spacing
        newPage.drawText(`Page ${pageNumber}`, {
          x: embeddedX,
          y: embeddedY - 15,
          size: 10,
          color: themeColors.muted
        });
        
        // Enhanced structured feedback renderer with better formatting
        const renderFeedbackSection = (
          section: FeedbackSection,
          startX: number,
          startY: number,
          maxWidth: number,
          page: any
        ): number => {
          let currentY = startY;

          // Draw section title with better spacing
          if (section.title) {
            currentY = drawText(
              section.title,
              startX,
              currentY,
              maxWidth,
              textConfig.fontSize.subheader,
              page,
              themeColors.primary,
              { isBold: true, isTitle: true }
            );
            currentY -= textConfig.spacing.line; // Extra space after title
          }

          // Draw section content with no truncation
          if (section.content) {
            currentY = drawText(
              section.content,
              startX + 10,
              currentY,
              maxWidth - 10,
              textConfig.fontSize.body,
              page,
              themeColors.muted
              // Removed maxLines to show full content
            );
          }

          // Draw subsections with enhanced formatting
          if (section.subsections && section.subsections.length > 0) {
            section.subsections.forEach((subsection) => {
              // Add spacing before subsection
              currentY -= textConfig.spacing.paragraph;

              // Subsection title in bold with bullet point
              if (subsection.title) {
                currentY = drawText(
                  `• ${subsection.title}`,
                  startX + 15,
                  currentY,
                  maxWidth - 15,
                  textConfig.fontSize.body, // Increased from small to body
                  page,
                  themeColors.primary, // Changed to primary for better visibility
                  { isBold: true }
                );
                currentY -= textConfig.spacing.line; // Space after title
              }

              // Subsection content with full text (no truncation)
              if (subsection.content) {
                // Parse structured content if it contains multiple parts
                const contentParts = subsection.content.split(/(?:Core Claim:|Evidence:|Reasoning:|Link to Question:|Analysis:)/);

                if (contentParts.length > 1) {
                  // Handle structured content with labels
                  const labels = subsection.content.match(/(?:Core Claim:|Evidence:|Reasoning:|Link to Question:|Analysis:)/g) || [];

                  for (let i = 0; i < labels.length && i < contentParts.length - 1; i++) {
                    const label = labels[i];
                    const content = contentParts[i + 1].trim();

                    if (content) {
                      // Draw label in bold
                      currentY = drawText(
                        label,
                        startX + 25,
                        currentY,
                        maxWidth - 25,
                        textConfig.fontSize.small,
                        page,
                        themeColors.secondary,
                        { isBold: true }
                      );

                      // Draw content
                      currentY = drawText(
                        content,
                        startX + 35,
                        currentY,
                        maxWidth - 35,
                        textConfig.fontSize.small,
                        page,
                        themeColors.lightMuted
                      );

                      currentY -= textConfig.spacing.line; // Space between parts
                    }
                  }
                } else {
                  // Regular content without structure
                  currentY = drawText(
                    subsection.content,
                    startX + 25,
                    currentY,
                    maxWidth - 25,
                    textConfig.fontSize.small,
                    page,
                    themeColors.lightMuted
                  );
                }
              }
            });
          }

          return currentY - textConfig.spacing.section; // Extra space after section
        };

        // Add feedback content on the right side with improved layout
        const feedbackX = Math.max(embeddedWidth + 120, margin + 500); // Increased spacing from embedded page
        const feedbackY = pageHeight - 120; // Consistent with embedded page positioning
        const feedbackWidth = pageWidth - feedbackX - margin; // Ensure it doesn't exceed right margin
        
        // Find questions that have feedback for this page
        const questionsForThisPage = evaluationData.questions.filter((question: QuestionBreakdown) => {
          if (question.structuredFeedback && question.structuredFeedback.length > 0) {
            return question.structuredFeedback.some(section =>
              section.subsections && section.subsections.some(subsection =>
                subsection.bounding_box &&
                subsection.bounding_box.page === pageNumber
              )
            );
          }
          if (question.paragraphs && question.paragraphs.length > 0) {
            return question.paragraphs.some(paragraph =>
              paragraph.boundingBox.page === pageNumber
            );
          }
          return false;
        });

        // Enhanced feedback rendering with better overflow handling
        if (questionsForThisPage.length > 0) {
          // Add feedback header
          let currentY = drawText(
            'Paragraph-by-paragraph analysis of the answer',
            feedbackX,
            feedbackY,
            feedbackWidth,
            textConfig.fontSize.header,
            newPage,
            themeColors.primary,
            { isTitle: true }
          );

          questionsForThisPage.forEach((question: QuestionBreakdown, questionIndex) => {
            // More generous space check - only skip if very little space left
            if (currentY < margin + 50) {
              // Add note about continuation
              drawText(
                '(Continued on next page...)',
                feedbackX,
                currentY,
                feedbackWidth,
                textConfig.fontSize.small,
                newPage,
                themeColors.muted
              );
              return; // Skip remaining questions for this page
            }

            // Question header (only show if multiple questions)
            if (questionsForThisPage.length > 1) {
              const questionHeader = `Question ${question.questionNumber}:`;
              currentY = drawText(
                questionHeader,
                feedbackX,
                currentY,
                feedbackWidth,
                textConfig.fontSize.subheader,
                newPage,
                themeColors.primary,
                { isBold: true, isTitle: true }
              );

              // Question score
              const scoreText = `Marks: ${question.marksAwarded}/${question.marksPossible} (${question.percentage}%)`;
              const scoreColor = question.percentage >= 80 ? themeColors.success :
                               question.percentage >= 60 ? themeColors.warning : themeColors.danger;
              currentY = drawText(
                scoreText,
                feedbackX + 10,
                currentY,
                feedbackWidth - 10,
                textConfig.fontSize.body,
                newPage,
                scoreColor
              );
            }

            // Render structured feedback if available
            if (question.structuredFeedback && question.structuredFeedback.length > 0) {
              const pageFeedback = question.structuredFeedback
                .map(section => ({
                  ...section,
                  subsections: section.subsections?.filter(subsection =>
                    subsection.bounding_box && subsection.bounding_box.page === pageNumber
                  ) || []
                }))
                .filter(section => section.subsections.length > 0);

              pageFeedback.forEach(section => {
                // Check if we have space for at least the section title
                if (currentY > margin + 30) {
                  currentY = renderFeedbackSection(
                    section,
                    feedbackX + 10,
                    currentY,
                    feedbackWidth - 10,
                    newPage
                  );
                }
              });
            } else if (question.feedback) {
              // Fallback to regular feedback (no truncation)
              currentY = drawText(
                question.feedback,
                feedbackX + 10,
                currentY,
                feedbackWidth - 10,
                textConfig.fontSize.body,
                newPage,
                themeColors.muted
                // Removed maxLines to show full content
              );
            }

            // Add spacing between questions (only if not the last question)
            if (questionIndex < questionsForThisPage.length - 1) {
              currentY -= textConfig.spacing.section;
            }
          });
        } else {
          // No feedback for this page
          drawText(
            'No specific feedback for this page',
            feedbackX,
            feedbackY - 50,
            feedbackWidth,
            textConfig.fontSize.body,
            newPage,
            themeColors.lightMuted
          );
        }

        // Add footer with consistent positioning
        newPage.drawText('Generated by AegisScholar', {
          x: margin,
          y: 40,
          size: textConfig.fontSize.tiny,
          color: themeColors.muted
        });

        newPage.drawText('AI-Powered Educational Assessment Platform', {
          x: margin,
          y: 30,
          size: textConfig.fontSize.tiny - 1,
          color: themeColors.lightMuted
        });
      }

      // Save and download the side-by-side PDF
      const sideBySidePdfBytes = await newPdfDoc.save();
      const blob = new Blob([new Uint8Array(sideBySidePdfBytes)], { type: 'application/pdf' });

      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${submissionData.studentName.replace(/\s+/g, '_')}_Annotated_Answer_Sheet_${new Date().toISOString().split('T')[0]}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      console.log('Side-by-side answer sheet with feedback created and downloaded successfully');

    } catch (error) {
      console.error('Error creating side-by-side answer sheet:', error);
      throw error;
    }
  };



  return (
    <div className="download-report-container space-y-3">
      <div className="flex flex-row gap-2">
        {submissionData.pdfUrl && (
          <button
            onClick={downloadSideBySideReport}
            disabled={isDownloadingSideBySide}
            className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-foreground bg-muted hover:bg-accent/50 border border-border rounded-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            title="Download Annotated Answer Sheet"
          >
            <ViewColumnsIcon className="w-4 h-4" />
            <span className="hidden sm:inline">
              {isDownloadingSideBySide ? 'Generating...' : 'Download Annotated Answer Sheet'}
            </span>
            <span className="sm:hidden">
              {isDownloadingSideBySide ? 'Generating...' : 'Annotated'}
            </span>
          </button>
        )}
      </div>

      {/* Development preview - remove in production */}
      {/* {process.env.NODE_ENV === 'development' && submissionData.detailedBreakdown?.questions?.length > 0 && (
        <div className="mt-4 space-y-2">
          <h3 className="text-sm font-medium text-foreground">Feedback Preview (Development Only):</h3>
          {submissionData.detailedBreakdown.questions.slice(0, 2).map((question, index) => (
            <div key={index} className="space-y-2">
              <h4 className="text-xs font-medium text-muted-foreground">Question {question.questionNumber}:</h4>
              {question.structuredFeedback?.map((section, sectionIndex) => (
                <div key={sectionIndex}>
                  {section.subsections?.slice(0, 1).map((subsection, subIndex) => (
                    <MarkdownPreview key={subIndex} content={subsection.content || ''} />
                  ))}
                </div>
              ))}
            </div>
          ))}
        </div>
      )} */}
    </div>
  );
};

export default DownloadStudentReport; 