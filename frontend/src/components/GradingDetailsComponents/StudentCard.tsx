import React, { useMemo, useState } from 'react';
import { AnswerSheetData, AnswerSheetResult } from '../../types/gradingTypes';
import { EVALUATION_CONFIG, CONSTANTS } from '../../config/evaluationConfig';
import { useSSEGradingProgress } from '../../hooks/useSSEGradingProgress';
import { StudentAvatar } from './StudentAvatar';
import { StudentInfo } from './StudentInfo';
import { GradingProgress } from './GradingProgress';
import { ScoreDisplay } from './ScoreDisplay';
import { ActionButton } from './ActionButton';
import { RefundButton } from './RefundButton';
import { calculatePercentage } from '../../utils/gradingUtils';


interface StudentCardProps {
    sheet: AnswerSheetData;
    onViewResults: (result: AnswerSheetResult) => void;
    formatResults: (sheet: AnswerSheetData) => AnswerSheetResult | null;
    onRefreshData?: () => Promise<void> | void; // Optional callback to refresh submission data
    submissionId?: string; // For refund functionality
}

export const StudentCard: React.FC<StudentCardProps> = ({
    sheet,
    onViewResults,
    formatResults,
    onRefreshData,
    submissionId
}) => {
    const [hasParsingError, setHasParsingError] = useState(false);

    // Parse evaluation data with error handling
    const parsedEvaluation = useMemo(() => {
        if (!sheet.evaluationResult) {
            setHasParsingError(false);
            return null;
        }
        
        try {
            const result = EVALUATION_CONFIG[CONSTANTS.CURRENT_FORMAT].parseEvaluation(sheet.evaluationResult);
            setHasParsingError(false);
            return result;
        } catch (error) {
            console.warn(`[StudentCard] Failed to parse evaluation for ${sheet.studentName}:`, error);
            setHasParsingError(true);
            return null;
        }
    }, [sheet.evaluationResult, sheet.studentName]);

    // Calculate scores
    const scores = useMemo(() => {
        if (!parsedEvaluation) return null;

        const totalMarks = parsedEvaluation.total_marks;
        const maxMarks = parsedEvaluation.maximum_possible_marks;
        let percentage = parsedEvaluation.percentage_score;

        if ((!percentage || percentage === 0) && totalMarks !== undefined && maxMarks !== undefined) {
            percentage = calculatePercentage(totalMarks, maxMarks);
        }

        return { totalMarks, maxMarks, percentage };
    }, [parsedEvaluation]);

    // SSE Progress tracking
    const sseState = useSSEGradingProgress({
        sheetId: sheet.id,
        hasEvaluationResult: !!parsedEvaluation, // Use parsedEvaluation instead of raw evaluationResult
        initialStatus: sheet.status
    });

    // Handle view results action
    const handleViewResults = () => {
        // Only proceed if we have evaluation results
        if (parsedEvaluation) {
            const result = formatResults(sheet);
            if (result) onViewResults(result);
        }
    };

    // Determine if actions should be shown
    // Show actions only when we have evaluation results (already graded)
    const showActions = !!parsedEvaluation;

    // Determine if refund button should be shown and if it's already refunded
    // Show refund button for sheets with 'error' status from database, regardless of SSE state
    const showRefundButton = sheet.status === 'error';
    const isSheetRefunded = sheet.refundStatus?.isRefunded || false;

    // Determine button text based on current state
    const getButtonText = () => {
        if (parsedEvaluation) {
            return "View Details";
        }
        if (hasParsingError) {
            return "Data Error";
        }
        return "View Details";
    };

    return (
        <div className={`flex items-center gap-4 p-3 sm:p-4 border border-border dark:border-border rounded-lg hover:shadow-sm dark:hover:shadow-lg hover:border-primary/20 dark:hover:border-primary/30 transition-all duration-200 relative ${
            hasParsingError ? 'border-destructive/30 bg-destructive/5' : ''
        }`}>
            {/* Parsing error indicator */}
            {hasParsingError && (
                <div className="absolute inset-0 bg-destructive/5 rounded-lg flex items-center justify-center">
                    <div className="flex items-center gap-2 text-destructive text-sm font-medium">
                        <span>⚠️ Data Error</span>
                    </div>
                </div>
            )}

            {/* Left: Avatar and Student Info */}
            <div className="flex items-center gap-3 flex-shrink-0">
                <StudentAvatar
                    name={sheet.studentName}
                    percentage={scores?.percentage}
                />
                <StudentInfo
                    name={sheet.studentName}
                    rollNumber={sheet.rollNumber}
                />
            </div>

            {/* Center: Progress Status */}
            <div className="flex-1 flex justify-center">
                {!showActions && (
                    <GradingProgress
                        progress={sseState.progress}
                        status={sseState.status}
                        isConnected={sseState.isConnected}
                        error={sseState.error}
                    />
                )}
            </div>

            {/* Right: Score and Action Buttons */}
            <div className="flex flex-col items-end gap-2 flex-shrink-0">
                {/* Score Display */}
                {parsedEvaluation && (
                    <ScoreDisplay
                        scores={scores}
                        hasError={sseState.status === 'error'}
                        isCompleted={sseState.status === 'completed'}
                    />
                )}

                {/* Action Buttons Container */}
                <div className="flex flex-col gap-2">
                    {/* View Details Button */}
                    {showActions && (
                        <ActionButton
                            onClick={handleViewResults}
                            label={getButtonText()}
                            shortLabel={parsedEvaluation ? "Details" : (hasParsingError ? "Error" : "Details")}
                            isLoading={false}
                            disabled={false}
                        />
                    )}

                    {/* Refund Button for Failed Sheets */}
                    {showRefundButton && !isSheetRefunded && submissionId && (
                        <RefundButton
                            submissionId={submissionId}
                            sheetId={sheet.id}
                            onRefundSuccess={onRefreshData}
                            isRefunded={isSheetRefunded}
                        />
                    )}
                </div>
            </div>
        </div>
    );
};
