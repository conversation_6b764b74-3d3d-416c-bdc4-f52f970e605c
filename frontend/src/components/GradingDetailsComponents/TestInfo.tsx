// components/GradingDetails/TestInfo.tsx
import React from 'react';
import { ArrowTrendingUpIcon } from '@heroicons/react/24/outline';
import { ClassStats } from '../../types/gradingTypes';

interface TestInfoProps {
    testDetails: {
        subject: string;
        className: string;
        date: string;
    };
    stats?: ClassStats | null;
}

export const TestInfo: React.FC<TestInfoProps> = ({ testDetails, stats }) => {
    const getPerformanceColor = (percentage: number): string => {
        if (percentage >= 80) return 'text-success-600 dark:text-success-400';
        if (percentage >= 60) return 'text-primary-600 dark:text-primary-400';
        if (percentage >= 40) return 'text-warning-600 dark:text-warning-400';
        return 'text-danger-600 dark:text-danger-400';
    };

    return (
        <div className="bg-card dark:bg-card rounded-lg shadow-sm border border-border p-2 sm:p-6">
            <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4">
                {/* Left side: Test details */}
                <div className="flex-1">
                    <h1 className="text-lg sm:text-xl lg:text-2xl font-bold font-['Space_Grotesk'] mb-2 text-foreground break-words">
                        {testDetails.subject || 'Unnamed Test'}
                    </h1>
                    <p className="text-sm sm:text-base text-muted-foreground break-words">
                        {testDetails.className} • {testDetails.date}
                    </p>
                </div>

                {/* Right side: Class statistics */}
                {stats && (
                    <div className="lg:flex-shrink-0">
                        {/* <div className="flex items-center gap-2">
                            <ArrowTrendingUpIcon className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0 text-muted-foreground" />
                            <h3 className="font-semibold text-foreground text-sm sm:text-base">
                                Class Statistics
                            </h3>
                        </div> */}
                        
                        <div className="grid grid-cols-4 gap-2 sm:gap-3">
                            <div className="p-2 rounded-md bg-muted/50">
                                <p className="text-xs sm:text-sm text-muted-foreground mb-1">Average</p>
                                <p className={`font-bold text-sm sm:text-base ${getPerformanceColor(stats.average)}`}>
                                    {stats.average.toFixed(1)}%
                                </p>
                            </div>

                            <div className="p-2 rounded-md bg-success-50 dark:bg-success-900/10">
                                <p className="text-xs sm:text-sm text-muted-foreground mb-1">Highest</p>
                                <p className="font-bold text-success-600 dark:text-success-400 text-sm sm:text-base">
                                    {stats.highest.toFixed(1)}%
                                </p>
                            </div>

                            <div className="p-2 rounded-md bg-danger-50 dark:bg-danger-900/10">
                                <p className="text-xs sm:text-sm text-muted-foreground mb-1">Lowest</p>
                                <p className="font-bold text-danger-600 dark:text-danger-400 text-sm sm:text-base">
                                    {stats.lowest.toFixed(1)}%
                                </p>
                            </div>

                            <div className="p-2 rounded-md bg-primary-50 dark:bg-primary-900/10">
                                <p className="text-xs sm:text-sm text-muted-foreground mb-1">Graded</p>
                                <p className="font-bold text-primary-600 dark:text-primary-400 text-sm sm:text-base">
                                    {stats.totalStudents}/{stats.totalSubmissions}
                                </p>
                            </div>
                        </div>
                    </div>
                )}

                {/* Show message when no stats available */}
                {!stats && (
                    <div className="lg:flex-shrink-0">
                        <div className="flex items-center gap-2 mb-3">
                            <ArrowTrendingUpIcon className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0 text-muted-foreground" />
                            <h3 className="font-semibold text-foreground text-sm sm:text-base">
                                Class Statistics
                            </h3>
                        </div>
                        <div className="text-center py-2">
                            <p className="text-xs sm:text-sm text-muted-foreground">
                                No graded submissions yet
                            </p>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};
