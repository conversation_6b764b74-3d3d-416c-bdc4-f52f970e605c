import React, { useState } from 'react';
import Markdown<PERSON>ender<PERSON> from './MarkdownRenderer';
import { previewMarkdownConversion } from '@/utils/markdownToPdf';

/**
 * Test component to preview how markdown content will be converted for PDF
 * This helps ensure the markdown-to-PDF conversion works correctly
 */
const MarkdownPdfTest: React.FC = () => {
  const [testMarkdown, setTestMarkdown] = useState(`# Question Analysis

## Strengths
- **Good understanding** of the topic
- Clear structure in the answer
- Relevant examples provided

## Areas for Improvement
1. More detailed explanations needed
2. Better use of \`technical terms\`
3. Include more recent data

### Specific Feedback
The student demonstrated a solid grasp of the fundamental concepts. However, the analysis could be enhanced by:

> "Including more contemporary examples would strengthen the argument significantly."

**Recommendation**: Focus on current affairs integration.

\`\`\`
Code blocks will be removed in PDF
\`\`\`

[External links](https://example.com) will show only the text.`);

  const [conversionResult, setConversionResult] = useState<any>(null);

  const handlePreview = () => {
    const result = previewMarkdownConversion(testMarkdown);
    setConversionResult(result);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="bg-card border border-border rounded-lg p-6">
        <h2 className="text-xl font-bold mb-4 text-foreground">Markdown to PDF Conversion Test</h2>
        <p className="text-sm text-muted-foreground mb-4">
          This tool helps test how markdown content will be converted for PDF annotation.
        </p>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Input Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-foreground">Input Markdown</h3>
            <textarea
              value={testMarkdown}
              onChange={(e) => setTestMarkdown(e.target.value)}
              className="w-full h-64 p-3 text-sm font-mono bg-muted border border-border rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-primary/50"
              placeholder="Enter markdown content here..."
            />
            <button
              onClick={handlePreview}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
            >
              Preview Conversion
            </button>
          </div>

          {/* Preview Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-foreground">Rendered Markdown</h3>
            <div className="h-64 p-3 bg-muted/30 border border-border rounded-md overflow-y-auto">
              <MarkdownRenderer content={testMarkdown} variant="default" />
            </div>
          </div>
        </div>

        {/* Conversion Results */}
        {conversionResult && (
          <div className="mt-8 space-y-6">
            <h3 className="text-lg font-semibold text-foreground">PDF Conversion Results</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Plain Text */}
              <div className="bg-muted/30 border border-border rounded-lg p-4">
                <h4 className="font-medium text-sm text-foreground mb-2">Plain Text (for PDF)</h4>
                <div className="text-xs text-muted-foreground bg-background p-3 rounded border font-mono whitespace-pre-wrap">
                  {conversionResult.plainText}
                </div>
              </div>

              {/* Key Points */}
              <div className="bg-muted/30 border border-border rounded-lg p-4">
                <h4 className="font-medium text-sm text-foreground mb-2">Key Points</h4>
                <ul className="text-xs text-muted-foreground space-y-1">
                  {conversionResult.keyPoints.map((point: string, index: number) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="text-primary">•</span>
                      <span>{point}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Text Chunks */}
              <div className="bg-muted/30 border border-border rounded-lg p-4 md:col-span-2">
                <h4 className="font-medium text-sm text-foreground mb-2">Text Chunks (for PDF layout)</h4>
                <div className="space-y-2">
                  {conversionResult.chunks.map((chunk: string, index: number) => (
                    <div key={index} className="text-xs text-muted-foreground bg-background p-2 rounded border">
                      <span className="text-primary font-medium">Chunk {index + 1}:</span> {chunk}
                    </div>
                  ))}
                </div>
              </div>

              {/* Short Summary */}
              <div className="bg-muted/30 border border-border rounded-lg p-4 md:col-span-2">
                <h4 className="font-medium text-sm text-foreground mb-2">Short Summary (200 chars max)</h4>
                <div className="text-xs text-muted-foreground bg-background p-3 rounded border">
                  {conversionResult.shortSummary}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Usage Instructions */}
      <div className="bg-muted/20 border border-border rounded-lg p-6">
        <h3 className="text-lg font-semibold text-foreground mb-3">How the PDF Conversion Works</h3>
        <div className="text-sm text-muted-foreground space-y-2">
          <p><strong>1. Markdown Processing:</strong> The system converts markdown to plain text by removing formatting markers while preserving content.</p>
          <p><strong>2. Text Chunking:</strong> Long content is split into manageable chunks that fit within PDF layout constraints.</p>
          <p><strong>3. Deterministic Layout:</strong> Each text block has predictable dimensions and positioning to prevent overlapping.</p>
          <p><strong>4. Consistent Spacing:</strong> All text elements use standardized font sizes and line heights for uniform appearance.</p>
        </div>
      </div>

      {/* Sample Feedback Data */}
      <div className="bg-muted/20 border border-border rounded-lg p-6">
        <h3 className="text-lg font-semibold text-foreground mb-3">Sample Feedback Examples</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button
            onClick={() => setTestMarkdown(`## Content Analysis
**Strengths:**
- Clear introduction with proper context
- Good use of examples from current affairs
- Logical flow of arguments

**Areas for Improvement:**
- Need more statistical data to support claims
- Conclusion could be more impactful
- Consider alternative perspectives

### Specific Suggestions
1. Add recent government reports
2. Include comparative analysis
3. Strengthen the concluding paragraph`)}
            className="p-3 text-left bg-background border border-border rounded-md hover:bg-muted/30 transition-colors"
          >
            <div className="font-medium text-sm text-foreground">Content Analysis</div>
            <div className="text-xs text-muted-foreground mt-1">Structured feedback with strengths and improvements</div>
          </button>

          <button
            onClick={() => setTestMarkdown(`# Question 2 Evaluation

## Understanding: 8/10
The student demonstrates **excellent comprehension** of the topic with clear definitions and proper context.

## Analysis: 6/10
Good analytical skills shown, but could benefit from:
- Deeper critical thinking
- More diverse perspectives
- Better synthesis of information

## Presentation: 7/10
> Well-structured answer with clear paragraphs and logical flow.

**Overall Score: 21/30**

### Recommendations
Focus on developing analytical depth and incorporating multiple viewpoints in future responses.`)}
            className="p-3 text-left bg-background border border-border rounded-md hover:bg-muted/30 transition-colors"
          >
            <div className="font-medium text-sm text-foreground">Detailed Evaluation</div>
            <div className="text-xs text-muted-foreground mt-1">Comprehensive scoring with specific feedback</div>
          </button>
        </div>
      </div>
    </div>
  );
};

export default MarkdownPdfTest;
