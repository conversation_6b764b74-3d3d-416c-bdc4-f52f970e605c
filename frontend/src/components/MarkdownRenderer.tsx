import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

interface MarkdownRendererProps {
    content: string;
    className?: string;
    variant?: 'default' | 'muted' | 'subsection';
}

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ 
    content, 
    className = "text-sm text-foreground leading-relaxed",
    variant = 'default'
}) => {
    const getVariantStyles = () => {
        switch (variant) {
            case 'muted':
                return {
                    textColor: 'text-muted-foreground',
                    headerColor: 'text-muted-foreground',
                    borderColor: 'border-primary/30',
                    bgColor: 'bg-muted/10'
                };
            case 'subsection':
                return {
                    textColor: 'text-muted-foreground',
                    headerColor: 'text-muted-foreground',
                    borderColor: 'border-primary/30',
                    bgColor: 'bg-muted/10'
                };
            default:
                return {
                    textColor: 'text-foreground',
                    headerColor: 'text-foreground',
                    borderColor: 'border-primary/40',
                    bgColor: 'bg-muted/20'
                };
        }
    };

    const styles = getVariantStyles();

    return (
        <div className={className}>
            <ReactMarkdown 
                remarkPlugins={[remarkGfm]}
                components={{
                    // Enhanced styling for better readability
                    p: ({ children }) => (
                        <p className={`mb-3 leading-relaxed ${styles.textColor}`}>
                            {children}
                        </p>
                    ),
                    
                    // Better list styling with proper spacing
                    ul: ({ children }) => (
                        <ul className={`list-disc list-inside mb-3 space-y-2 pl-2 ${styles.textColor}`}>
                            {children}
                        </ul>
                    ),
                    ol: ({ children }) => (
                        <ol className={`list-decimal list-inside mb-3 space-y-2 pl-2 ${styles.textColor}`}>
                            {children}
                        </ol>
                    ),
                    li: ({ children }) => (
                        <li className={`leading-relaxed mb-1 ${styles.textColor}`}>
                            {children}
                        </li>
                    ),
                    
                    // Enhanced text styling
                    strong: ({ children }) => (
                        <strong className={`font-semibold ${styles.textColor}`}>
                            {children}
                        </strong>
                    ),
                    em: ({ children }) => (
                        <em className={`italic ${styles.textColor}`}>
                            {children}
                        </em>
                    ),
                    
                    // Better header styling
                    h1: ({ children }) => (
                        <h1 className={`text-lg font-bold mb-3 ${styles.headerColor} border-b border-border pb-1`}>
                            {children}
                        </h1>
                    ),
                    h2: ({ children }) => (
                        <h2 className={`text-base font-semibold mb-2 ${styles.headerColor}`}>
                            {children}
                        </h2>
                    ),
                    h3: ({ children }) => (
                        <h3 className={`text-sm font-semibold mb-2 ${styles.headerColor}`}>
                            {children}
                        </h3>
                    ),
                    
                    // Enhanced blockquote styling
                    blockquote: ({ children }) => (
                        <blockquote className={`border-l-4 ${styles.borderColor} pl-4 italic ${styles.textColor} ${styles.bgColor} py-2 rounded-r mb-3`}>
                            {children}
                        </blockquote>
                    ),
                    
                    // Better code styling
                    code: ({ children }) => (
                        <code className={`bg-muted px-1.5 py-0.5 rounded text-xs font-mono ${styles.textColor}`}>
                            {children}
                        </code>
                    ),
                    pre: ({ children }) => (
                        <pre className="bg-muted p-3 rounded text-xs font-mono overflow-x-auto mb-3 border border-border">
                            {children}
                        </pre>
                    ),
                    
                    // Handle line breaks better
                    br: () => <br className="mb-2" />,
                }}
            >
                {content}
            </ReactMarkdown>
        </div>
    );
};

export default MarkdownRenderer; 