import React, { useState } from "react";
import MarkdownRenderer from "./MarkdownRenderer";
import {
    parseJsonEvaluationData,
    type EvaluationBreakdown,
    type FeedbackSection
} from "@/utils/jsonEvaluationParser";
import { 
    BoundingBox, 
    ParagraphAnalysis, 
    QuestionFeedback, 
    JsonQuestionEvaluation 
} from "@/types/aegisGrader";

// --- Question Breakdown Function ---
export const parseQuestionBreakdown = parseJsonEvaluationData;

// --- Structured Feedback Component ---
const StructuredFeedbackDisplay: React.FC<{
    feedback: string;
    structuredFeedback?: FeedbackSection[];
    questionNumber: string;
}> = ({ feedback, structuredFeedback, questionNumber }) => {
    const [isExpanded, setIsExpanded] = useState(false);
    const [expandedSections, setExpandedSections] = useState<Set<number>>(new Set());

    const toggleSection = (index: number) => {
        const newExpanded = new Set(expandedSections);
        if (newExpanded.has(index)) {
            newExpanded.delete(index);
        } else {
            newExpanded.add(index);
        }
        setExpandedSections(newExpanded);
    };

    // If we have structured feedback, use it; otherwise fall back to plain text
    if (structuredFeedback && structuredFeedback.length > 0) {
        return (
            <div className="space-y-4">
                {structuredFeedback.map((section, index) => (
                    <div key={index} className="border border-border rounded-lg overflow-hidden">
                        <button
                            onClick={() => toggleSection(index)}
                            className="w-full p-3 bg-muted/20 hover:bg-muted/30 transition-colors text-left flex items-center justify-between"
                        >
                            <h5 className="font-medium text-foreground">{section.title}</h5>
                            <span className="text-muted-foreground text-sm">
                                {expandedSections.has(index) ? '−' : '+'}
                            </span>
                        </button>

                        {expandedSections.has(index) && (
                            <div className="p-3 bg-card border-t border-border">
                                <MarkdownRenderer
                                    content={section.content}
                                    variant="default"
                                />

                                {section.subsections && section.subsections.length > 0 && (
                                    <div className="space-y-3">
                                        {section.subsections.map((subsection, subIndex) => (
                                            <div key={subIndex} className="pl-4 border-l-2 border-primary/20">
                                                <h6 className="font-medium text-sm text-foreground mb-1">
                                                    {subsection.title}
                                                </h6>
                                                <MarkdownRenderer
                                                    content={subsection.content}
                                                    variant="subsection"
                                                />
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                ))}
            </div>
        );
    }

    // Fallback to markdown rendering with expand/collapse for long feedback
    const isLongFeedback = feedback.length > 500;
    const displayText = isLongFeedback && !isExpanded
        ? `${feedback.substring(0, 500)}...`
        : feedback;

    return (
        <div className="space-y-2">
            <div className="p-3 bg-muted/30 border border-border rounded">
                <MarkdownRenderer
                    content={displayText}
                    variant="default"
                />
            </div>

            {isLongFeedback && (
                <button
                    onClick={() => setIsExpanded(!isExpanded)}
                    className="text-xs text-primary hover:underline transition-colors"
                >
                    {isExpanded ? 'Show less' : 'Show full feedback'}
                </button>
            )}
        </div>
    );
};

// --- Display Component for Question Breakdown ---
const QuestionBreakdownDisplay: React.FC<{ evaluationData: EvaluationBreakdown | null }> = ({ evaluationData }) => {
    // Handle null or invalid data
    if (!evaluationData) {
        return (
            <div className="bg-card border border-border rounded-lg p-6 text-center">
                <p className="text-muted-foreground">Unable to load question breakdown data.</p>
                <p className="text-xs text-muted-foreground mt-2">
                    Please check if the evaluation data is properly formatted.
                </p>
            </div>
        );
    }

    // Handle empty questions array
    if (!evaluationData.questions || evaluationData.questions.length === 0) {
        return (
            <div className="bg-card border border-border rounded-lg p-6 text-center">
                <p className="text-muted-foreground">No question breakdown available.</p>
                <p className="text-xs text-muted-foreground mt-2">
                    The evaluation may not contain detailed question-wise analysis.
                </p>
            </div>
        );
    }

    // Check if all questions have errors
    const allQuestionsHaveErrors = evaluationData.questions.every(q => 
        q.feedback.includes('Error:') || q.feedback.includes('Evaluation Error:') || q.feedback.includes('Parsing Error:')
    );

    if (allQuestionsHaveErrors) {
        return (
            <div className="bg-card border border-destructive/20 rounded-lg p-6 text-center">
                <p className="text-destructive font-medium mb-2">Evaluation Processing Error</p>
                <p className="text-sm text-muted-foreground mb-4">
                    There was an issue processing the evaluation data. This may be due to malformed responses from the AI model.
                </p>
                <div className="space-y-2 text-left">
                    {evaluationData.questions.map((question, index) => (
                        <div key={index} className="text-xs text-muted-foreground p-2 bg-muted/30 rounded">
                            <strong>Question {question.questionNumber}:</strong> {question.feedback}
                        </div>
                    ))}
                </div>
                <p className="text-xs text-muted-foreground mt-4">
                    Please try submitting the evaluation again. If the problem persists, contact support.
                </p>
            </div>
        );
    }
    const getPerformanceColor = (percentage: number): string => {
        if (percentage >= 80) return "text-primary";
        if (percentage >= 60) return "text-foreground";
        if (percentage >= 40) return "text-muted-foreground";
        return "text-destructive";
    };

    const getPerformanceBg = (percentage: number): string => {
        if (percentage >= 80) return "bg-primary/5 border-primary/20";
        if (percentage >= 60) return "bg-accent/30 border-border";
        if (percentage >= 40) return "bg-muted/50 border-border";
        return "bg-destructive/5 border-destructive/20";
    };

    const getPerformanceBadge = (percentage: number): string => {
        if (percentage >= 80) return "bg-primary/10 text-primary border-primary/20";
        if (percentage >= 60) return "bg-accent text-foreground border-border";
        if (percentage >= 40) return "bg-muted text-muted-foreground border-border";
        return "bg-destructive/10 text-destructive border-destructive/20";
    };

    return (
        <div className="space-y-4 lg:space-y-6 p-2 lg:p-0">

            {/* Individual Question Breakdown */}
            <div className="space-y-3 lg:space-y-4">
                {evaluationData.questions.map((question) => {
                    // Check if this question has an error
                    const hasError = question.feedback.includes('Error:') || 
                                   question.feedback.includes('Evaluation Error:') || 
                                   question.feedback.includes('Parsing Error:');
                    
                    return (
                        <div key={question.questionNumber} className={`bg-card border rounded-lg overflow-hidden ${
                            hasError ? 'border-destructive/20' : 'border-border'
                        }`}>
                            {/* Question Header */}
                            <div className={`p-3 lg:p-4 border-b ${
                                hasError ? 'bg-destructive/5 border-destructive/20' : getPerformanceBg(question.percentage)
                            }`}>
                                <div className="flex items-center justify-between">
                                    <h3 className="text-sm lg:text-base font-semibold text-foreground">
                                        {question.questionNumber}
                                        {hasError && (
                                            <span className="ml-2 text-xs text-destructive bg-destructive/10 px-2 py-1 rounded">
                                                Error
                                            </span>
                                        )}
                                    </h3>
                                    <div className="flex items-center gap-2">
                                        <p className={`text-sm lg:text-base font-semibold ${
                                            hasError ? 'text-destructive' : getPerformanceColor(question.percentage)
                                        }`}>
                                            {question.marksAwarded}/{question.marksPossible}
                                        </p>
                                        <p className="text-xs text-muted-foreground">marks</p>
                                    </div>
                                </div>
                            </div>

                            {/* Question Content */}
                            <div className="p-3 lg:p-4 space-y-3 lg:space-y-4">
                                {/* Feedback */}
                                {question.feedback && (
                                    <div>
                                        {hasError ? (
                                            <div className="bg-destructive/5 border border-destructive/20 rounded-lg p-4">
                                                <h4 className="text-sm font-medium text-destructive mb-2">Evaluation Error</h4>
                                                <p className="text-sm text-muted-foreground">{question.feedback}</p>
                                                <p className="text-xs text-muted-foreground mt-2">
                                                    This question could not be evaluated properly. Please try again or contact support if the issue persists.
                                                </p>
                                            </div>
                                        ) : (
                                            <StructuredFeedbackDisplay
                                                feedback={question.feedback}
                                                structuredFeedback={question.structuredFeedback}
                                                questionNumber={question.questionNumber}
                                            />
                                        )}
                                    </div>
                                )}
                            </div>
                        </div>
                    );
                })}
            </div>
        </div>
    );
};

// --- Error Boundary Component ---
class QuestionBreakdownErrorBoundary extends React.Component<
    { children: React.ReactNode },
    { hasError: boolean; error?: Error }
> {
    constructor(props: { children: React.ReactNode }) {
        super(props);
        this.state = { hasError: false };
    }

    static getDerivedStateFromError(error: Error) {
        return { hasError: true, error };
    }

    componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
        console.error('QuestionBreakdown Error:', error, errorInfo);
    }

    render() {
        if (this.state.hasError) {
            return (
                <div className="bg-card border border-destructive/20 rounded-lg p-6 text-center">
                    <p className="text-destructive font-medium mb-2">Error displaying question breakdown</p>
                    <p className="text-sm text-muted-foreground">
                        There was an issue processing the evaluation data. Please try refreshing the page.
                    </p>
                    <button
                        className="mt-3 px-4 py-2 bg-primary text-primary-foreground rounded text-sm hover:bg-primary/90"
                        onClick={() => this.setState({ hasError: false })}
                    >
                        Try Again
                    </button>
                </div>
            );
        }

        return this.props.children;
    }
}

// --- Main Export with Error Boundary ---
const QuestionBreakdownWithErrorBoundary: React.FC<{ evaluationData: EvaluationBreakdown | null }> = ({ evaluationData }) => {
    return (
        <QuestionBreakdownErrorBoundary>
            <QuestionBreakdownDisplay evaluationData={evaluationData} />
        </QuestionBreakdownErrorBoundary>
    );
};

// validateJsonEvaluationData is now imported from jsonEvaluationParser

export default QuestionBreakdownWithErrorBoundary;
