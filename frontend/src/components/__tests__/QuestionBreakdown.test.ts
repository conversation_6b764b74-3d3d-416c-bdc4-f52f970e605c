import { parseQuestionBreakdown } from '../QuestionBreakdown';
import { validateJsonEvaluationData } from '../../utils/jsonEvaluationParser';

// Test data based on the new JSON evaluation output structure
const testEvaluationData = [
    {
        questionNumber: "1",
        subject: "History",
                    feedback: {
                marks_awarded: {
                    score: "5/10",
                    justification: "The answer attempts to address the question by highlighting the significance of cave paintings, but it lacks depth and specific examples from ancient Indian cave paintings beyond a superficial mention of prehistoric sites."
                },
            overall_assessment: "The answer attempts to address the question by highlighting the significance of cave paintings, but it lacks depth and specific examples from ancient Indian cave paintings beyond a superficial mention of prehistoric sites. The connection to aesthetic sensitivity and creativeness is not well-developed.",
            paragraph_by_paragraph_analysis: [
                {
                    section_title: "Introduction",
                    paragraph_number: 1,
                    student_text_snippet: "Cave paintings are important historical sources.",
                    core_claim: "Cave paintings are important historical sources",
                    evidence_present: "No specific evidence provided",
                    reasoning_quality: "Basic statement without analysis",
                    link_to_question: "Does not directly engage with aesthetic sensitivity and creativeness",
                    analysis_notes: "The introduction is generic and states the importance of paintings as historical sources but does not directly engage with the 'aesthetic sensitivity and creativeness' aspect of the question.",
                    specific_improvements: ["Directly address the aesthetic sensitivity aspect", "Provide specific examples of creative expression"],
                    evidence_to_add: "Include specific cave painting examples with artistic analysis",
                    structural_fix: "Restructure to focus on creative and aesthetic elements"
                },
                {
                    section_title: "Body",
                    paragraph_number: 2,
                    student_text_snippet: "Sohagihat and Lakhudiyar cave sites show ancient art.",
                    core_claim: "Specific cave sites demonstrate ancient artistic expression",
                    evidence_present: "Mentions specific sites but no detailed analysis",
                    reasoning_quality: "Lists sites without connecting to creativity",
                    link_to_question: "Provides examples but lacks artistic analysis",
                    analysis_notes: "The body lists some cave sites but fails to elaborate on the artistic elements or the creative expression evident in them.",
                    specific_improvements: ["Analyze artistic techniques used", "Connect sites to creative expression"],
                    evidence_to_add: "Include specific artistic elements and techniques from these sites",
                    structural_fix: "Add analysis of artistic creativity and aesthetic elements"
                }
            ],
            positive_observations: [
                "Mentions relevant cave sites like Sohagihat and Lakhudiyar",
                "Attempts to connect to the question's theme"
            ],
            critical_deficiencies: [
                "Lacks analytical depth and specific examples",
                "Does not adequately explore aesthetic sensitivity and creativeness"
            ],
            prescriptive_actions_for_rectification: "Focus on developing analytical skills and providing specific examples from cave paintings to demonstrate aesthetic sensitivity and creativeness."
        }
    },
    {
        questionNumber: "14",
        subject: "History",
                    feedback: {
                marks_awarded: {
                    score: "9/15",
                    justification: "The answer effectively discusses the dual impact of globalization on the Indian craft industry, providing relevant positive and negative examples."
                },
            overall_assessment: "The answer effectively discusses the dual impact of globalization on the Indian craft industry, providing relevant positive and negative examples. It demonstrates a good understanding of the topic and its nuances.",
            paragraph_by_paragraph_analysis: [
                {
                    section_title: "Introduction",
                    paragraph_number: 1,
                    student_text_snippet: "Globalization has both positive and negative impacts on Indian crafts.",
                    core_claim: "Globalization has dual impacts on Indian crafts",
                    evidence_present: "No specific evidence provided in introduction",
                    reasoning_quality: "Clear thesis statement",
                    link_to_question: "Sets up the dual nature of the impact",
                    analysis_notes: "Good introduction that sets up the dual nature of the impact.",
                    specific_improvements: ["Add specific examples of impacts", "Provide data or case studies"],
                    evidence_to_add: "Include statistics or specific examples of globalization effects",
                    structural_fix: "Add a roadmap for the argument structure"
                }
            ],
            positive_observations: [
                "Good understanding of the topic",
                "Provides relevant examples"
            ],
            critical_deficiencies: [
                "Could benefit from more detailed analysis",
                "Some areas need more specific examples"
            ],
            prescriptive_actions_for_rectification: "Continue developing analytical skills and provide more specific examples."
        }
    }
];



// Test edge cases
const edgeCaseTests = [
    // Empty array
    [],
    // Null data
    null,
    // Undefined data
    undefined,
    // Invalid string
    ["invalid json content"],
    // Malformed JSON
    ['{"questionNumber": "1", "feedback": {'],
    // Missing required elements
    [{"questionNumber": "1"}],
    // Error response
    [{"error": "Malformed input data provided"}],
];

describe('QuestionBreakdown Robustness Tests', () => {
    test('should parse valid evaluation data correctly', () => {
        const result = parseQuestionBreakdown(testEvaluationData);
        
        expect(result).not.toBeNull();
        expect(result?.totalMarks).toBe(14); // 5 + 9
        expect(result?.maxMarks).toBe(25); // 10 + 15
        expect(result?.questions).toHaveLength(2);
        expect(result?.questions[0].questionNumber).toBe("1");
        expect(result?.questions[0].marksAwarded).toBe(5);
        expect(result?.questions[0].marksPossible).toBe(10);
        expect(result?.questions[0].percentage).toBe(50);
    });

    test('should handle edge cases gracefully', () => {
        edgeCaseTests.forEach((testCase, index) => {
            const result = parseQuestionBreakdown(testCase);
            // Should not throw errors and should return null for invalid data
            expect(() => parseQuestionBreakdown(testCase)).not.toThrow();
            console.log(`Edge case ${index + 1}: ${result ? 'Parsed successfully' : 'Returned null as expected'}`);
        });
    });

    test('should validate evaluation data correctly', () => {
        const validation = validateJsonEvaluationData(testEvaluationData);
        expect(validation.isValid).toBe(true);
        expect(validation.errors).toHaveLength(0);
    });

    test('should detect invalid data in validation', () => {
        const validation = validateJsonEvaluationData(null);
        expect(validation.isValid).toBe(false);
        expect(validation.errors.length).toBeGreaterThan(0);
    });

    test('should handle criteria breakdown correctly with new format', () => {
        const result = parseQuestionBreakdown(testEvaluationData);

        // JSON format creates a simplified criteria breakdown
        expect(result?.questions[0].criteriaBreakdown).toHaveLength(1);
        expect(result?.questions[0].criteriaBreakdown[0].criterion).toBe("Overall Score");
        expect(result?.questions[0].criteriaBreakdown[0].score).toBe("5");
        expect(result?.questions[0].criteriaBreakdown[0].maxScore).toBe("10");
    });

    test('should extract feedback from complex nested structures', () => {
        const result = parseQuestionBreakdown(testEvaluationData);

        expect(result?.questions[0].feedback).toContain("Overall Assessment:");
        expect(result?.questions[0].feedback).toContain("Detailed Analysis:");
        expect(result?.questions[0].feedback).toContain("Positive Observations:");
        expect(result?.questions[0].feedback).toContain("Critical Deficiencies:");
        expect(result?.questions[0].feedback).toContain("Recommendations:");
    });

    test('should handle different marks_possible tag names', () => {
        const result = parseQuestionBreakdown(testEvaluationData);

        // Both questions extract marks from justification text
        expect(result?.questions[0].marksPossible).toBe(10);
        expect(result?.questions[1].marksPossible).toBe(15);
    });

    test('should handle new score format (5/10)', () => {
        const dataWithNewScoreFormat = [
            {
                questionNumber: "1",
                subject: "History",
                feedback: {
                    marks_awarded: {
                        score: "7/12",
                        justification: "Good answer with room for improvement."
                    },
                    overall_assessment: "Good answer with room for improvement.",
                    paragraph_by_paragraph_analysis: [],
                    positive_observations: ["Good knowledge."],
                    critical_deficiencies: ["Needs more analysis."],
                    prescriptive_actions_for_rectification: "Improve analytical skills."
                }
            }
        ];

        const result = parseQuestionBreakdown(dataWithNewScoreFormat);

        expect(result).not.toBeNull();
        expect(result?.questions[0].marksAwarded).toBe(7);
        expect(result?.questions[0].marksPossible).toBe(12);
        expect(result?.questions[0].percentage).toBe(58); // 7/12 = 58%
    });

    test('should generate proper markdown format for feedback', () => {
        const dataWithMarkdownFeedback = [
            {
                questionNumber: "1",
                subject: "History",
                feedback: {
                    marks_awarded: {
                        score: "8/15",
                        justification: "Good answer with some areas for improvement."
                    },
                    overall_assessment: "This answer demonstrates good understanding but lacks critical analysis.",
                    paragraph_by_paragraph_analysis: [
                        {
                            section_title: "Introduction",
                            paragraph_number: 1,
                            student_text_snippet: "The topic is important.",
                            core_claim: "The topic is important",
                            evidence_present: "No specific evidence provided",
                            reasoning_quality: "Basic statement",
                            link_to_question: "Sets up the topic",
                            analysis_notes: "The introduction sets up the topic well.",
                            specific_improvements: ["Add specific importance points", "Provide evidence for importance"],
                            evidence_to_add: "Include specific examples or data",
                            structural_fix: "Add a clear thesis statement"
                        }
                    ],
                    positive_observations: [
                        "Good factual knowledge",
                        "Clear writing style"
                    ],
                    critical_deficiencies: [
                        "Lacks analytical depth",
                        "Missing critical evaluation"
                    ],
                    prescriptive_actions_for_rectification: "Focus on developing analytical skills and critical thinking."
                }
            }
        ];

        const result = parseQuestionBreakdown(dataWithMarkdownFeedback);

        expect(result).not.toBeNull();
        expect(result?.questions[0].feedback).toContain('## Overall Assessment');
        expect(result?.questions[0].feedback).toContain('## Positive Observations');
        expect(result?.questions[0].feedback).toContain('## Critical Deficiencies');
        expect(result?.questions[0].feedback).toContain('## Recommendations');
        expect(result?.questions[0].feedback).toContain('- Good factual knowledge');
        expect(result?.questions[0].feedback).toContain('- Lacks analytical depth');
    });

    test('should format recommendations with numbered lists properly', () => {
        const dataWithNumberedRecommendations = [
            {
                questionNumber: "1",
                subject: "History",
                feedback: {
                    marks_awarded: {
                        score: "6/15",
                        justification: "Answer needs improvement."
                    },
                    overall_assessment: "Basic answer with room for improvement.",
                    paragraph_by_paragraph_analysis: [],
                    positive_observations: ["Some correct facts"],
                    critical_deficiencies: ["Lacks depth"],
                    prescriptive_actions_for_rectification: "To improve: 1. Develop a clear thesis statement. 2. Provide more evidence. 3. Analyze critically. 4. Structure your argument better."
                }
            }
        ];

        const result = parseQuestionBreakdown(dataWithNumberedRecommendations);

        expect(result).not.toBeNull();
        expect(result?.questions[0].feedback).toContain('## Recommendations');
        // The recommendations should be properly formatted with line breaks
        expect(result?.questions[0].feedback).toContain('1. Develop a clear thesis statement');
        expect(result?.questions[0].feedback).toContain('2. Provide more evidence');
        expect(result?.questions[0].feedback).toContain('3. Analyze critically');
        expect(result?.questions[0].feedback).toContain('4. Structure your argument better');
    });

    test('should handle escaped JSON format', () => {
        const escapedJsonData = `\`\`\`json
{
    "questionNumber": "1",
    "subject": "History",
    "feedback": {
        "marks_awarded": {
            "score": 15,
            "justification": "The score of 15 out of 25 is assigned."
        },
        "overall_assessment": "Good answer.",
        "paragraph_by_paragraph_analysis": [],
        "positive_observations": ["Good knowledge."],
        "critical_deficiencies": ["Needs improvement."],
        "prescriptive_actions_for_rectification": "Improve skills."
    }
}
\`\`\``;

        const result = parseQuestionBreakdown(escapedJsonData);

        expect(result).not.toBeNull();
        expect(result?.totalMarks).toBe(15);
        expect(result?.maxMarks).toBe(25);
        expect(result?.questions).toHaveLength(1);
    });

    test('should calculate percentages correctly', () => {
        const result = parseQuestionBreakdown(testEvaluationData);

        expect(result?.overallPercentage).toBe(56); // 14/25 = 56%
        expect(result?.questions[0].percentage).toBe(50); // 5/10 = 50%
        expect(result?.questions[1].percentage).toBe(60); // 9/15 = 60%
    });

    test('should handle criteria with missing maxScore correctly', () => {
        const dataWithMissingMaxScore = [
            {
                questionNumber: "1",
                subject: "History",
                feedback: {
                    marks_awarded: {
                        score: 80,
                        justification: "The score of 80 out of 100 is assigned."
                    },
                    overall_assessment: "Good work with some areas for improvement.",
                    paragraph_by_paragraph_analysis: [],
                    positive_observations: ["Good content"],
                    critical_deficiencies: ["Needs improvement"],
                    prescriptive_actions_for_rectification: "Continue improving"
                }
            }
        ];

        const result = parseQuestionBreakdown(dataWithMissingMaxScore);

        expect(result).not.toBeNull();
        expect(result?.questions).toHaveLength(1);

        const question = result?.questions[0];
        expect(question?.criteriaBreakdown).toHaveLength(1);

        // Check that the overall score criterion is parsed correctly
        const overallCriterion = question?.criteriaBreakdown[0];
        expect(overallCriterion?.criterion).toBe('Overall Score');
        expect(overallCriterion?.score).toBe('80');
        expect(overallCriterion?.maxScore).toBe('100');
    });
});
