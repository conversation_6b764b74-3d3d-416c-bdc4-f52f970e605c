import React from 'react';
import {
    XMarkIcon,
    DocumentArrowDownIcon,
    CreditCardIcon,
    CalendarIcon,
    IdentificationIcon,
    BanknotesIcon,
    CheckCircleIcon,
    UserIcon,
    EnvelopeIcon,
    CurrencyRupeeIcon,
    DocumentTextIcon
} from '@heroicons/react/24/outline';
import { ReceiptModalProps } from '@/types/billing';
import { useUser } from '@/contexts/userContext';
import { createPortal } from 'react-dom';

const ReceiptModal: React.FC<ReceiptModalProps> = ({
    isOpen,
    onClose,
    transaction,
    onDownload
}) => {
    const { user } = useUser();

    if (!isOpen) return null;

    const formatAmount = (amount: number) => {
        return `₹${(amount / 100).toFixed(2)}`;
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-IN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const handleDownload = () => {
        // Create a professional receipt with theme colors and Razorpay branding
        const receiptContent = `
            <html>
                <head>
                    <title>Payment Receipt - ${transaction.transactionId}</title>
                    <style>
                        * { margin: 0; padding: 0; box-sizing: border-box; }
                        body {
                            font-family: 'Space Grotesk', Urbanist, sans-serif;
                            line-height: 1.6;
                            color: #0a0f29;
                            background: #ffffff;
                            max-width: 800px;
                            margin: 0 auto;
                            padding: 40px 20px;
                        }

                        .receipt-container {
                            background: #ffffff;
                            border-radius: 12px;
                            box-shadow: 0 4px 20px rgba(20, 31, 82, 0.1);
                            overflow: hidden;
                        }

                        .header {
                            background: #cce6ff;
                            color: #000000;
                            padding: 30px;
                            position: relative;
                            display: flex;
                            justify-content: space-between;
                            align-items: flex-start;
                        }

                        .header-left {
                            position: relative;
                            z-index: 1;
                            display: flex;
                            align-items: flex-start;
                            gap: 4px;
                        }

                        .header-right {
                            position: relative;
                            z-index: 1;
                            display: flex;
                            align-items: center;
                            gap: 8px;
                        }

                        .company-logo {
                            width: 32px;
                            height: 32px;
                            background: white;
                            border-radius: 6px;
                            padding: 2px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            margin-top: 2px;
                        }

                        .company-logo img {
                            width: 100%;
                            height: 100%;
                            object-fit: contain;
                        }

                        .company-info {
                            display: flex;
                            flex-direction: column;
                            align-items: flex-start;
                            gap: 4px;
                        }

                        .company-name {
                            font-size: 28px;
                            font-weight: 700;
                            letter-spacing: -0.5px;
                            line-height: 1;
                        }

                        .receipt-info {
                            display: flex;
                            flex-direction: column;
                            gap: 8px;
                            margin-top: 8px;
                        }

                        .receipt-title {
                            font-size: 16px;
                            font-weight: 400;
                            opacity: 0.9;
                        }

                        .success-badge {
                            display: inline-flex;
                            align-items: center;
                            gap: 8px;
                            background: rgba(51, 168, 82, 0.2);
                            color: #33a852;
                            padding: 6px 12px;
                            border-radius: 16px;
                            font-size: 12px;
                            font-weight: 600;
                            border: 1px solid rgba(51, 168, 82, 0.3);
                            width: fit-content;
                        }

                        .razorpay-header {
                            display: flex;
                            flex-direction: column;
                            align-items: flex-end;
                            gap: 6px;
                        }

                        .razorpay-text {
                            font-size: 11px;
                            opacity: 0.7;
                            font-weight: 400;
                        }

                        .razorpay-logo-header {
                            height: 24px;
                            width: auto;
                            background: white;
                            padding: 4px 8px;
                            border-radius: 4px;
                        }

                        .content {
                            padding: 40px;
                        }

                        .section {
                            margin-bottom: 32px;
                        }

                        .section-title {
                            font-size: 18px;
                            font-weight: 600;
                            color: #000000;
                            margin-bottom: 16px;
                            padding-bottom: 8px;
                            border-bottom: 2px solid #e5e5e5;
                            display: flex;
                            align-items: center;
                            gap: 8px;
                        }

                        .section-icon {
                            width: 20px;
                            height: 20px;
                            color: #000000;
                            display: inline-flex;
                            align-items: center;
                            justify-content: center;
                        }

                        .info-grid {
                            display: grid;
                            gap: 16px;
                        }

                        .info-row {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            padding: 12px 0;
                            border-bottom: 1px solid #f0f5ff;
                        }

                        .info-row:last-child {
                            border-bottom: none;
                        }

                        .label {
                            font-weight: 500;
                            color: #52587a;
                            font-size: 14px;
                        }

                        .value {
                            font-weight: 600;
                            color: #0a0f29;
                            text-align: right;
                            font-size: 14px;
                        }

                        .value.mono {
                            font-family: 'Courier New', monospace;
                            background: #f0f5ff;
                            padding: 4px 8px;
                            border-radius: 4px;
                            font-size: 12px;
                        }

                        .highlight-section {
                            background: #cce6ff;
                            border-radius: 12px;
                            padding: 24px;
                            border: 1px solid #e0e4eb;
                        }

                        .total-section {
                            background: #cce6ff;
                            color: #000000;
                            border-radius: 12px;
                            padding: 24px;
                            margin-top: 24px;
                        }

                        .total-row {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            font-size: 20px;
                            font-weight: 700;
                        }

                        .credits-highlight {
                            color: #33a852;
                            font-size: 18px;
                            font-weight: 700;
                        }

                        .footer {
                            background: #f0f5ff;
                            padding: 30px;
                            text-align: center;
                            border-top: 1px solid #e0e4eb;
                        }

                        .footer-content {
                            margin-bottom: 20px;
                        }

                        .footer-title {
                            font-size: 16px;
                            font-weight: 600;
                            color: #141f52;
                            margin-bottom: 8px;
                        }

                        .footer-text {
                            font-size: 14px;
                            color: #52587a;
                            margin-bottom: 4px;
                        }

                        .razorpay-section {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            gap: 8px;
                            padding: 16px;
                            background: white;
                            border-radius: 8px;
                            border: 1px solid #e0e4eb;
                            margin-top: 16px;
                        }

                        .razorpay-text {
                            font-size: 12px;
                            color: #000000;
                        }

                        .razorpay-logo {
                            height: 16px;
                            opacity: 0.8;
                        }

                        .generated-info {
                            font-size: 12px;
                            color: #52587a;
                            margin-top: 16px;
                            padding-top: 16px;
                            border-top: 1px solid #e0e4eb;
                        }

                        @media print {
                            body { padding: 20px; }
                            .receipt-container { box-shadow: none; }
                        }
                    </style>
                </head>
                <body>
                    <div class="receipt-container">
                        <div class="header">
                            <div class="header-left">
                                <div class="company-info">
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <div class="company-logo">
                                            <img src="/logo_accent.png" alt="AegisScholar Logo" />
                                        </div>
                                        <div class="company-name">AegisScholar</div>
                                    </div>
                                    <div class="receipt-info">
                                        <div class="receipt-title">Payment Receipt</div>
                                        <div class="success-badge">
                                            ✓ Payment Successful
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="header-right">
                                <div class="razorpay-header">
                                    <div class="razorpay-text">Powered by</div>
                                    <img src="/Razorpay_logo.png" alt="Razorpay" class="razorpay-logo-header" />
                                </div>
                            </div>
                        </div>

                        <div class="content">
                            <div class="section">
                                <div class="section-title">
                                    <svg class="section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    Transaction Details
                                </div>
                                <div class="info-grid">
                                    <div class="info-row">
                                        <span class="label">Transaction ID</span>
                                        <span class="value mono">${transaction.transactionId}</span>
                                    </div>
                                    <div class="info-row">
                                        <span class="label">Date & Time</span>
                                        <span class="value">${formatDate(transaction.createdAt)}</span>
                                    </div>
                                    <div class="info-row">
                                        <span class="label">Status</span>
                                        <span class="value" style="color: #33a852;">✓ ${transaction.status}</span>
                                    </div>
                                    ${transaction.payment ? `
                                    <div class="info-row">
                                        <span class="label">Razorpay Payment ID</span>
                                        <span class="value mono">${transaction.payment.razorpayPaymentId}</span>
                                    </div>
                                    <div class="info-row">
                                        <span class="label">Razorpay Order ID</span>
                                        <span class="value mono">${transaction.payment.razorpayOrderId}</span>
                                    </div>
                                    ` : ''}
                                </div>
                            </div>

                            <div class="section">
                                <div class="section-title">
                                    <svg class="section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                    Customer Information
                                </div>
                                <div class="info-grid">
                                    <div class="info-row">
                                        <span class="label">Name</span>
                                        <span class="value">${user?.firstName} ${user?.lastName}</span>
                                    </div>
                                    <div class="info-row">
                                        <span class="label">Email</span>
                                        <span class="value">${user?.email}</span>
                                    </div>
                                    <div class="info-row">
                                        <span class="label">Customer ID</span>
                                        <span class="value mono">${user?.id}</span>
                                    </div>
                                </div>
                            </div>

                            ${transaction.payment ? `
                            <div class="section">
                                <div class="section-title">
                                    <svg class="section-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                                    </svg>
                                    Purchase Details
                                </div>
                                <div class="highlight-section">
                                    <div class="info-grid">
                                        <div class="info-row">
                                            <span class="label">Package</span>
                                            <span class="value">${transaction.payment.packageName}</span>
                                        </div>
                                        <div class="info-row">
                                            <span class="label">Credits Added</span>
                                            <span class="value credits-highlight">+${transaction.creditAmount} credits</span>
                                        </div>
                                        <div class="info-row">
                                            <span class="label">Price per Credit</span>
                                            <span class="value">${formatAmount(transaction.payment.amount / transaction.creditAmount)}</span>
                                        </div>
                                        <div class="info-row">
                                            <span class="label">Currency</span>
                                            <span class="value">${transaction.payment.currency || 'INR'}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            ` : ''}

                            ${transaction.payment ? `
                            <div class="total-section">
                                <div class="total-row">
                                    <span>Total Amount Paid</span>
                                    <span>${formatAmount(transaction.payment.amount)}</span>
                                </div>
                            </div>
                            ` : ''}
                        </div>

                        <div class="footer">
                            <div class="footer-content">
                                <div class="footer-title">Thank you for your purchase! 🎉</div>
                                <div class="footer-text">Your credits have been added to your account and are ready to use.</div>
                                <div class="footer-text">For support or questions, contact us at <strong><EMAIL></strong></div>
                            </div>

                            <div class="generated-info">
                                Receipt generated on ${new Date().toLocaleDateString('en-IN', {
                                    year: 'numeric',
                                    month: 'long',
                                    day: 'numeric',
                                    hour: '2-digit',
                                    minute: '2-digit'
                                })}
                            </div>
                        </div>
                    </div>
                </body>
            </html>
        `;

        const printWindow = window.open('', '_blank');
        if (printWindow) {
            printWindow.document.write(receiptContent);
            printWindow.document.close();
            printWindow.print();
        }

        if (onDownload) {
            onDownload();
        }
    };

    return createPortal(
        <div className="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="bg-card rounded-xl border border-border shadow-2xl max-w-2xl w-full max-h-[90vh] flex flex-col">
                {/* Header */}
                <div className="bg-muted/20 p-6 border-b border-border rounded-t-xl">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                            <div className="p-2 bg-primary/10 rounded-lg">
                                <DocumentTextIcon className="w-6 h-6 text-primary" />
                            </div>
                            <div>
                                <h2 className="text-xl font-semibold text-foreground">Payment Receipt</h2>
                                <p className="text-sm text-muted-foreground">Transaction confirmation</p>
                            </div>
                        </div>
                        <button
                            onClick={onClose}
                            className="p-2 hover:bg-muted/50 rounded-lg transition-colors"
                        >
                            <XMarkIcon className="w-5 h-5 text-muted-foreground hover:text-foreground" />
                        </button>
                    </div>
                </div>

                {/* Scrollable Content */}
                <div className="flex-1 overflow-y-auto">
                    <div className="p-6 space-y-6">
                        {/* Transaction Status */}
                        <div className="flex items-center justify-center gap-3 p-4 bg-success/10 rounded-xl border border-success/20">
                            <div className="p-1 bg-success/10 rounded-full">
                                <CheckCircleIcon className="w-5 h-5 text-success" />
                            </div>
                            <span className="text-success font-semibold">
                                Payment Successful
                            </span>
                        </div>

                        {/* Transaction Details */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
                                <IdentificationIcon className="w-5 h-5 text-primary" />
                                Transaction Details
                            </h3>
                            <div className="bg-muted/30 rounded-xl p-4 space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div className="space-y-2">
                                        <div className="flex items-center gap-2">
                                            <span className="text-sm font-medium text-muted-foreground">Transaction ID</span>
                                        </div>
                                        <p className="font-mono text-sm bg-card border border-border p-3 rounded-lg">
                                            {transaction.transactionId}
                                        </p>
                                    </div>
                                    <div className="space-y-2">
                                        <div className="flex items-center gap-2">
                                            <span className="text-sm font-medium text-muted-foreground">Date & Time</span>
                                        </div>
                                        <p className="text-sm bg-card border border-border p-3 rounded-lg">
                                            {formatDate(transaction.createdAt)}
                                        </p>
                                    </div>
                                </div>

                                {transaction.payment && (
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                                        <div className="space-y-2">
                                            <div className="flex items-center gap-2">
                                                <span className="text-sm font-medium text-muted-foreground">Payment ID</span>
                                            </div>
                                            <p className="font-mono text-sm bg-card border border-border p-3 rounded-lg">
                                                {transaction.payment.razorpayPaymentId}
                                            </p>
                                        </div>
                                        <div className="space-y-2">
                                            <div className="flex items-center gap-2">
                                                <span className="text-sm font-medium text-muted-foreground">Order ID</span>
                                            </div>
                                            <p className="font-mono text-sm bg-card border border-border p-3 rounded-lg">
                                                {transaction.payment.razorpayOrderId}
                                            </p>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Purchase Details */}
                        {transaction.payment && (
                            <div className="space-y-4">
                                <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
                                    <img src="/money_filled.png" alt="Credits" className="w-5 h-5" />
                                    Purchase Details
                                </h3>
                                <div className="bg-primary/5 rounded-xl p-5 border border-primary/20">
                                    <div className="space-y-4">
                                        <div className="flex justify-between items-center">
                                            <span className="text-muted-foreground font-medium">Package:</span>
                                            <span className="font-semibold text-foreground">{transaction.payment.packageName}</span>
                                        </div>
                                        <div className="flex justify-between items-center">
                                            <span className="text-muted-foreground font-medium">Credits Added:</span>
                                            <div className="flex items-center gap-2">
                                                <span className="font-semibold text-success text-lg">
                                                    +{transaction.creditAmount}
                                                </span>
                                            </div>
                                        </div>
                                        <div className="flex justify-between items-center">
                                            <span className="text-muted-foreground font-medium">Price per credit:</span>
                                            <span className="font-semibold text-foreground">{formatAmount(transaction.payment.amount / transaction.creditAmount)}</span>
                                        </div>
                                        <div className="border-t border-primary/20 pt-4">
                                            <div className="flex justify-between items-center">
                                                <span className="font-bold text-foreground">Total:</span>
                                                <span className="font-bold text-xl text-primary">
                                                    {formatAmount(transaction.payment.amount)}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* Customer Information */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
                                <UserIcon className="w-5 h-5 text-primary" />
                                Customer Information
                            </h3>
                            <div className="bg-muted/30 rounded-xl p-4 space-y-3">
                                <div className="flex justify-between items-center">
                                    <div className="flex items-center gap-2">
                                        <UserIcon className="w-4 h-4 text-muted-foreground" />
                                        <span className="text-muted-foreground font-medium">Name:</span>
                                    </div>
                                    <span className="font-semibold">{user?.firstName} {user?.lastName}</span>
                                </div>
                                <div className="flex justify-between items-center">
                                    <div className="flex items-center gap-2">
                                        <EnvelopeIcon className="w-4 h-4 text-muted-foreground" />
                                        <span className="text-muted-foreground font-medium">Email:</span>
                                    </div>
                                    <span className="font-semibold">{user?.email}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Footer */}
                <div className="bg-muted/20 border-t border-border p-6 rounded-b-xl">
                    <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
                        <div className="text-center sm:text-left">
                            <p className="text-sm font-medium text-foreground">
                                Thank you for your purchase! 🎉
                            </p>
                            <p className="text-xs text-muted-foreground mt-1">
                                For support, contact <NAME_EMAIL>
                            </p>
                        </div>
                        <button
                            onClick={handleDownload}
                            className="flex items-center gap-2 px-6 py-3 bg-primary text-primary-foreground rounded-xl hover:bg-primary/90 transition-colors shadow-lg hover:shadow-xl"
                        >
                            <DocumentArrowDownIcon className="w-4 h-4" />
                            Download Receipt
                        </button>
                    </div>
                </div>
            </div>
        </div>,
        document.body
    );
};

export default ReceiptModal;
