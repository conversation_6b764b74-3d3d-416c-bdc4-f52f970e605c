import React, { useState } from 'react';
import { XMarkIcon, CreditCardIcon, ArrowPathIcon, SparklesIcon, CheckIcon, InformationCircleIcon, MinusIcon, PlusIcon, BanknotesIcon, ShieldCheckIcon, LockClosedIcon, ClockIcon, BoltIcon } from '@heroicons/react/24/outline';
import { useAxiosPrivate } from '@/hooks/useAxiosPrivate';
import { toast } from 'react-toastify';
import { createPortal } from 'react-dom';

interface PricingTier {
    name: string;
    minCredits: number;
    maxCredits: number | null;
    pricePerCredit: number;
    discount: number;
    description: string;
    badge?: string;
}

interface CreditPurchaseModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSuccess?: (creditsAdded: number, newBalance: number) => void;
}

declare global {
    interface Window {
        Razorpay: any;
    }
}

const PRICING_TIERS: PricingTier[] = [
    {
        name: 'Professional',
        minCredits: 250,
        maxCredits: 5000,
        pricePerCredit: 200,
        discount: 0,
        description: 'Perfect for individual educators and small teams',
        badge: ''
    },
    {
        name: 'Premium',
        minCredits: 5001,
        maxCredits: 10000,
        pricePerCredit: 175,
        discount: 12.5,
        description: 'Great for departments and larger institutions',
        badge: 'Most Popular'
    },
    {
        name: 'Enterprise',
        minCredits: 10001,
        maxCredits: null,
        pricePerCredit: 0,
        discount: 0,
        description: 'Custom solutions for large organizations',
        badge: ''
    }
];

const CreditPurchaseModal: React.FC<CreditPurchaseModalProps> = ({
    isOpen,
    onClose,
    onSuccess
}) => {
    const [selectedCredits, setSelectedCredits] = useState(250);
    const [customAmount, setCustomAmount] = useState('250');
    const [purchasing, setPurchasing] = useState(false);
    const axiosPrivate = useAxiosPrivate();

    const getCurrentTier = (credits: number): PricingTier => {
        if (credits >= 10001) return PRICING_TIERS[2];
        if (credits >= 5001) return PRICING_TIERS[1];
        return PRICING_TIERS[0];
    };

    // Calculates subtotal and total (no GST)
    const calculatePrice = (credits: number) => {
        const tier = getCurrentTier(credits);
        if (tier.name === 'Enterprise') {
            return { total: 0, perCredit: 0, savings: 0 };
        }
        const total = credits * tier.pricePerCredit;
        const originalPrice = credits * 200; // Always compare to Professional
        const savings = originalPrice - total;
        return { total, perCredit: tier.pricePerCredit, savings };
    };

    const currentTier = getCurrentTier(selectedCredits);
    const pricing = calculatePrice(selectedCredits);

    const handleCreditChange = (value: string) => {
        const cleanValue = value.replace(/[^0-9]/g, '');
        const numValue = parseInt(cleanValue) || 0;
        setCustomAmount(cleanValue);
        if (numValue >= 250) {
            setSelectedCredits(numValue);
        }
    };

    const handleQuickSelect = (credits: number) => {
        setSelectedCredits(credits);
        setCustomAmount(credits.toString());
    };

    const handlePurchase = async () => {
        if (selectedCredits < 250) {
            toast.error('Minimum purchase is 250 credits');
            return;
        }
        if (currentTier.name === 'Enterprise') {
            toast.info('Please contact us for Enterprise pricing');
            return;
        }
        try {
            setPurchasing(true);
            const customPackage = {
                credits: selectedCredits,
                amount: pricing.total,
                name: `${selectedCredits} Credits - ${currentTier.name}`,
                description: `${selectedCredits} AegisGrader evaluations`,
                pricePerCredit: pricing.perCredit
            };
            const packageId = `CUSTOM_${selectedCredits}`;
            const orderResponse = await axiosPrivate.post('/api/credits/order', {
                packageId: packageId,
                customPackage: customPackage
            });
            const { orderId, amount, currency, packageInfo } = orderResponse.data.data;
            const options = {
                key: import.meta.env.VITE_RAZORPAY_KEY_ID,
                amount: amount,
                currency: currency,
                name: 'AegisScholar',
                description: `Purchase ${packageInfo.name}`,
                order_id: orderId,
                handler: async (response: any) => {
                    try {
                        const verifyResponse = await axiosPrivate.post('/api/credits/verify', {
                            razorpay_order_id: response.razorpay_order_id,
                            razorpay_payment_id: response.razorpay_payment_id,
                            razorpay_signature: response.razorpay_signature
                        });
                        const { creditsAdded, newBalance } = verifyResponse.data.data;
                        toast.success(`Successfully purchased ${creditsAdded} credits!`, { autoClose: 5000 });
                        if (onSuccess) {
                            onSuccess(creditsAdded, newBalance);
                        }
                        onClose();
                    } catch (error: any) {
                        toast.error(error.response?.data?.message || 'Payment verification failed');
                    }
                },
                prefill: { name: 'AegisScholar User', email: '', contact: '' },
                theme: { color: '' },
                modal: { ondismiss: () => setPurchasing(false) }
            };
            const razorpay = new window.Razorpay(options);
            razorpay.open();
        } catch (error: any) {
            toast.error(error.response?.data?.message || 'Failed to create payment order');
            setPurchasing(false);
        }
    };

    const handleIncrement = () => {
        const next = selectedCredits + 100;
        setSelectedCredits(next);
        setCustomAmount(next.toString());
    };
    const handleDecrement = () => {
        const next = selectedCredits - 100;
        if (next >= 250) {
            setSelectedCredits(next);
            setCustomAmount(next.toString());
        }
    };

    if (!isOpen) return null;

    return createPortal(
        <div className="fixed inset-0 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="bg-card border border-border rounded-lg shadow-2xl max-w-3xl w-full max-h-[90dvh] overflow-auto flex flex-col">
                {/* Header */}
                <div className="flex items-center justify-between p-6 border-b border-border">
                    <div className="flex items-center gap-3">
                        <BanknotesIcon className="w-7 h-7 text-primary" />
                        <div>
                            <h2 className="text-2xl font-['Space_Grotesk'] font-bold text-foreground">Purchase Credits</h2>
                            <p className="lg:block hidden text-sm text-muted-foreground">Choose the number of credits and plan</p>
                        </div>
                    </div>
                    <button onClick={onClose} className="p-2 hover:bg-muted rounded-xl" disabled={purchasing}>
                        <XMarkIcon className="w-6 h-6 text-muted-foreground" />
                    </button>
                </div>

                {/* Content: 2 columns for left (plans) and right (input + summary) */}
                <div className="p-4 flex flex-col md:flex-row gap-8">
                    {/* Left: Plans (column) */}
                    <div className="md:w-1/2 w-full flex flex-col gap-4">
                        {PRICING_TIERS.map((tier) => (
                            <button
                                key={tier.name}
                                type="button"
                                className={`
                  border rounded-lg p-4 w-full transition relative text-left shadow
                  bg-card hover:border-primary/60 focus:outline-none focus:ring-1 focus:ring-primary
                  ${currentTier.name === tier.name
                                        ? 'ring-1 ring-primary border-primary bg-primary/5'
                                        : 'border-border'}
                  ${tier.badge === 'Most Popular' ? 'ring-1 ring-accent border-accent bg-accent' : ''}
                `}
                                onClick={() => {
                                    if (tier.name === 'Enterprise') handleQuickSelect(15000);
                                    else handleQuickSelect(tier.minCredits);
                                }}
                            >
                                <div className="flex items-center justify-between mb-2">
                                    <span className="font-['Space_Grotesk'] font-semibold text-lg text-foreground tracking-wide">
                                        {tier.name}
                                    </span>
                                </div>
                                <p className="text-xs text-muted-foreground mb-1">{tier.description}</p>
                                <div className="flex items-center justify-between mt-2">
                                    <span className="text-xs text-muted-foreground">{tier.minCredits.toLocaleString()}+ credits</span>
                                    <span className="font-bold text-lg text-foreground">
                                        {tier.name === 'Enterprise' ? 'Contact Us' : `₹${tier.pricePerCredit}`}{tier.name !== 'Enterprise' && <span className="text-xs text-muted-foreground"> /credit</span>}
                                    </span>
                                </div>
                                <div className="absolute top-3 right-4 flex gap-2">
                                    {tier.badge && (
                                        <span className={`
                      rounded-full px-3 py-1 text-xs font-semibold flex items-center gap-1
                      ${tier.badge === 'Most Popular'
                                                ? 'bg-accent/70 text-white shadow-lg'
                                                : 'bg-card border border-primary px-2 py-0.5 text-primary'}
                    `}>
                                            {tier.badge === "Most Popular" && <SparklesIcon className="w-3 h-3" />}
                                            {tier.badge}
                                        </span>
                                    )}
                                    {currentTier.name === tier.name && (
                                        <span className="rounded-full bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 p-1 border border-green-300 dark:border-green-700">
                                            <CheckIcon className="w-4 h-4" />
                                        </span>
                                    )}
                                </div>
                            </button>
                        ))}
                    </div>

                    {/* Right: Credit input + summary */}
                    <div className="md:w-1/2 w-full flex flex-col gap-4">
                        {/* Credit input with minus/plus */}
                        <label className="text-md font-['Space_Grotesk'] font-medium text-foreground">Credits</label>
                        <div className="flex items-center gap-3 bg-card border border-border rounded-lg px-4 py-3">
                            <button
                                className="hover:bg-muted rounded-full p-1.5 disabled:opacity-50"
                                onClick={handleDecrement}
                                disabled={selectedCredits <= 250}
                                type="button"
                                aria-label="Decrease"
                            >
                                <MinusIcon className="w-6 h-6 text-primary" />
                            </button>
                            <input
                                type="text"
                                inputMode="numeric"
                                pattern="[0-9]*"
                                value={customAmount}
                                onChange={(e) => handleCreditChange(e.target.value)}
                                className="flex-1 text-center text-lg font-semibold bg-transparent outline-none"
                                min={250}
                                style={{ minWidth: 80 }}
                            />
                            <button
                                className="hover:bg-muted rounded-full p-1.5"
                                onClick={handleIncrement}
                                type="button"
                                aria-label="Increase"
                            >
                                <PlusIcon className="w-6 h-6 text-primary" />
                            </button>
                        </div>
                        {selectedCredits < 250 && (
                            <p className="text-xs text-destructive">Minimum purchase is 250 credits</p>
                        )}

                        {/* Order summary */}
                        <div>
                            {selectedCredits >= 250 && currentTier.name !== 'Enterprise' && (
                                <div className="p-4 rounded-lg border border-border bg-muted/50 flex flex-col gap-2 shadow-sm">
                                    <div className="flex items-center gap-2 font-semibold text-foreground mb-2 text-base">
                                        <SparklesIcon className="w-5 h-5" />
                                        Order Summary
                                    </div>
                                    <div className="flex justify-between text-sm">
                                        <span className="text-muted-foreground">Plan</span>
                                        <span>{currentTier.name}</span>
                                    </div>
                                    <div className="flex justify-between text-sm">
                                        <span className="text-muted-foreground">Credits</span>
                                        <span>{selectedCredits.toLocaleString()}</span>
                                    </div>
                                    <div className="flex justify-between text-sm">
                                        <span className="text-muted-foreground">Price per credit</span>
                                        <span>₹{pricing.perCredit}</span>
                                    </div>
                                    {pricing.savings > 0 && (
                                        <div className="flex justify-between text-xs text-green-600 dark:text-green-400">
                                            <span>You save</span>
                                            <span>₹{pricing.savings.toLocaleString()}</span>
                                        </div>
                                    )}
                                    <div className="flex justify-between text-lg font-bold pt-2 border-t border-border mt-2">
                                        <span className="text-foreground">Total</span>
                                        <span className="text-foreground">₹{pricing.total.toLocaleString()}</span>
                                    </div>
                                </div>
                            )}
                            {currentTier.name === 'Enterprise' && (
                                <div className="p-4 rounded-lg border border-border bg-muted/50 flex flex-col items-start gap-2">
                                    <span className="font-bold text-foreground mb-1">Enterprise Pricing</span>
                                    <span className="text-sm text-muted-foreground mb-3">
                                        For orders over 10,000 credits, we offer custom pricing and dedicated support.
                                    </span>
                                    <a
                                        href="mailto:<EMAIL>?subject=Enterprise%20Credit%20Pricing%20Inquiry"
                                        className="px-4 py-2 bg-primary text-primary-foreground rounded-lg text-sm font-medium"
                                    >
                                        Contact Us
                                    </a>
                                </div>
                            )}
                        </div>

                        {/* Security badges - positioned at bottom right */}
                        <div className="mt-auto p-4">
                            <div className="grid grid-cols-2 gap-3">
                                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                    <ShieldCheckIcon className="w-4 h-4 text-green-500" />
                                    <span>Secure Payment</span>
                                </div>
                                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                    <LockClosedIcon className="w-4 h-4 text-blue-500" />
                                    <span>256-bit SSL</span>
                                </div>
                                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                    <BoltIcon className="w-4 h-4 text-purple-500" />
                                    <span>Instant Processing</span>
                                </div>
                                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                    <CheckIcon className="w-4 h-4 text-green-500" />
                                    <span>No Hidden Fees</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Footer */}
                <div className="flex gap-3 p-4 md:p-6 border-t border-border bg-card">
                    <button
                        onClick={onClose}
                        className="flex-1 px-4 py-2 md:py-3 border border-border rounded-lg text-foreground hover:bg-muted"
                        disabled={purchasing}
                    >
                        Cancel
                    </button>
                    <button
                        onClick={
                            currentTier.name === 'Enterprise'
                                ? () => window.open('mailto:<EMAIL>?subject=Enterprise%20Credit%20Pricing%20Inquiry', '_blank')
                                : handlePurchase
                        }
                        disabled={selectedCredits < 250 || purchasing}
                        className="flex-1 px-4 py-2 md:py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2 font-semibold text-lg"
                    >
                        {purchasing ? (
                            <>
                                <ArrowPathIcon className="w-5 h-5 animate-spin" />
                                Processing...
                            </>
                        ) : currentTier.name === 'Enterprise' ? (
                            'Contact Us'
                        ) : (
                            <>
                                Buy
                                <img src="/money_filled.png" alt="Coin" className="w-5 h-5" />
                                {selectedCredits}
                            </>
                        )}
                    </button>
                </div>

                {/* Powered by Razorpay */}
                <div className="flex items-center justify-center gap-2 py-3 bg-card">
                    <span className="text-xs text-muted-foreground">Powered by</span>
                    <div className='dark:bg-white rounded-sm p-1'>
                        <img
                            src="/Razorpay_logo.png"
                            alt="Razorpay"
                            className="h-4 opacity-80"
                        />
                    </div>
                </div>
            </div>
        </div>,
        document.body
    );
};

export default CreditPurchaseModal;
