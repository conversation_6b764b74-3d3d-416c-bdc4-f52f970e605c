// config/evaluationConfig.ts
import { parseEvaluationForGradingDetails, type ParsedEvaluation } from '@/utils/jsonEvaluationParser';

/**
 * Interface for evaluation parsers that handle different data formats
 */
interface EvaluationParser {
    parseEvaluation: (evaluationResult: any) => ParsedEvaluation | null;
}

/**
 * Configuration for different evaluation data formats
 * This allows for easy migration between different data structures
 */
export const EVALUATION_CONFIG: Record<string, EvaluationParser> = {
    /**
     * Current format: Array with markdown/XML string
     * This is the active format being used in production
     */
    CURRENT: {
        parseEvaluation: (evaluationResult: any): ParsedEvaluation | null => {
            try {
                return parseEvaluationForGradingDetails(evaluationResult);
            } catch (error) {
                console.error('Error parsing current format evaluation:', error);
                return null;
            }
        }
    },

    /**
     * Future format: Direct object format
     * This is prepared for easy migration when the API changes
     */
    FUTURE_OBJECT: {
        parseEvaluation: (evaluationResult: any): ParsedEvaluation | null => {
            try {
                if (!evaluationResult?.evaluation) {
                    console.warn('Future format evaluation result missing evaluation property');
                    return null;
                }
                return evaluationResult.evaluation;
            } catch (error) {
                console.error('Error parsing future format evaluation:', error);
                return null;
            }
        }
    },

    /**
     * Legacy format: For backward compatibility
     * Handles older evaluation result structures
     */
    LEGACY: {
        parseEvaluation: (evaluationResult: any): ParsedEvaluation | null => {
            try {
                // Handle legacy format if needed
                if (typeof evaluationResult === 'string') {
                    const parsed = JSON.parse(evaluationResult);
                    return parseEvaluationForGradingDetails(parsed);
                }
                return parseEvaluationForGradingDetails(evaluationResult);
            } catch (error) {
                console.error('Error parsing legacy format evaluation:', error);
                return null;
            }
        }
    }
};

/**
 * Application constants for consistent behavior across components
 */
export const CONSTANTS = {
    // UI timing constants
    TOAST_DELAY: 2000,
    DOWNLOAD_DELAY: 1000,
    SSE_RETRY_DELAY: 1000,
    DEBOUNCE_DELAY: 300,

    // Current active format
    CURRENT_FORMAT: 'CURRENT' as keyof typeof EVALUATION_CONFIG,

    // Grade performance thresholds
    GRADE_THRESHOLDS: {
        EXCELLENT: 80,
        GOOD: 60,
        AVERAGE: 40,
        POOR: 0
    },

    // Progress tracking
    PROGRESS: {
        PENDING: 0,
        PROCESSING: 50,
        EVALUATING: 90,
        COMPLETED: 100
    },

    // SSE connection settings
    SSE_CONFIG: {
        RETRY_ATTEMPTS: 3,
        RETRY_DELAY: 1000,
        CONNECTION_TIMEOUT: 30000,
        HEARTBEAT_INTERVAL: 10000
    },

    // Search and pagination
    SEARCH_MIN_LENGTH: 2,
    DEFAULT_PAGE_SIZE: 20,
    MAX_SEARCH_RESULTS: 100,

    // File handling
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    ALLOWED_FILE_TYPES: ['pdf', 'doc', 'docx', 'txt'],

    // API endpoints (can be overridden by environment)
    API_ENDPOINTS: {
        BASE_URL: process.env.NODE_ENV === 'production' 
            ? 'https://api.aegisscholar.com' 
            : 'http://localhost:8080',
        GRADING_PROGRESS: '/api/aegisGrader/getCurrentProgress',
        SUBMISSION_DETAILS: '/api/submissions',
        DOWNLOAD_RESULTS: '/api/download'
    }
} as const;

/**
 * Utility functions for evaluation configuration
 */
export const EvaluationConfigUtils = {
    /**
     * Get the current active parser
     */
    getCurrentParser: (): EvaluationParser => {
        return EVALUATION_CONFIG[CONSTANTS.CURRENT_FORMAT];
    },

    /**
     * Parse evaluation with fallback to different formats
     */
    parseWithFallback: (evaluationResult: any): ParsedEvaluation | null => {
        // Try current format first
        let result = EVALUATION_CONFIG[CONSTANTS.CURRENT_FORMAT].parseEvaluation(evaluationResult);
        if (result) return result;

        // Fallback to legacy format
        result = EVALUATION_CONFIG.LEGACY.parseEvaluation(evaluationResult);
        if (result) {
            console.warn('Used legacy format parser as fallback');
            return result;
        }

        // Try future format as last resort
        result = EVALUATION_CONFIG.FUTURE_OBJECT.parseEvaluation(evaluationResult);
        if (result) {
            console.warn('Used future format parser as fallback');
            return result;
        }

        console.error('All evaluation parsers failed');
        return null;
    },

    /**
     * Validate evaluation result structure
     */
    validateEvaluationResult: (evaluationResult: any): boolean => {
        if (!evaluationResult) return false;
        
        // Basic structure validation
        if (typeof evaluationResult !== 'object' && typeof evaluationResult !== 'string') {
            return false;
        }

        return true;
    },

    /**
     * Get parser by format name
     */
    getParser: (formatName: keyof typeof EVALUATION_CONFIG): EvaluationParser | null => {
        return EVALUATION_CONFIG[formatName] || null;
    },

    /**
     * Check if format is supported
     */
    isFormatSupported: (formatName: string): formatName is keyof typeof EVALUATION_CONFIG => {
        return formatName in EVALUATION_CONFIG;
    }
};

/**
 * Type exports for better TypeScript support
 */
export type EvaluationFormat = keyof typeof EVALUATION_CONFIG;
export type { EvaluationParser, ParsedEvaluation };

/**
 * Development utilities (only enabled in development)
 */
if (process.env.NODE_ENV === 'development') {
    // Add debug utilities
    (window as any).__EVALUATION_CONFIG_DEBUG__ = {
        EVALUATION_CONFIG,
        CONSTANTS,
        EvaluationConfigUtils,
        // Helper to test different parsers
        testParser: (formatName: EvaluationFormat, data: any) => {
            const parser = EvaluationConfigUtils.getParser(formatName);
            return parser ? parser.parseEvaluation(data) : null;
        }
    };
}
