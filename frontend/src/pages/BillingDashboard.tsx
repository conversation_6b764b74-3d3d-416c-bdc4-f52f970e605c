import React, { useState, useEffect } from 'react';
import {
    CreditCardIcon,
    ChartBarIcon,
    DocumentArrowDownIcon,
    CheckCircleIcon
} from '@heroicons/react/24/outline';
import { PaymentTransaction } from '@/types/billing';
import ReceiptModal from '@/components/billing/ReceiptModal';
import BillingErrorBoundary from '@/components/billing/BillingErrorBoundary';
import CreditPurchaseModal from '@/components/credits/CreditPurchaseModal';
import { useAxiosPrivate } from '@/hooks/useAxiosPrivate';
import { toast } from 'react-toastify';
import PulsatingDots from '@/components/PulsatingDotsLoader';
import { fetchWithCache } from '@/utils/cacheUtil';

interface BillingData {
    creditBalance: {
        currentBalance: number;
        totalEarned: number;
        totalSpent: number;
        totalPurchased: number;
        totalAmountSpent: number;
        lastUpdated: string;
    };
    usageAnalytics: {
        totalEvaluations: number;
        averageCreditsPerDay: number;
        mostUsedFeature: string;
    };
    recentTransactions: PaymentTransaction[];
}

const BillingDashboard: React.FC = () => {
    const [billingData, setBillingData] = useState<BillingData | null>(null);
    const [loading, setLoading] = useState(true);
    const [selectedTransaction, setSelectedTransaction] = useState<PaymentTransaction | null>(null);
    const [showReceiptModal, setShowReceiptModal] = useState(false);
    const [showPurchaseModal, setShowPurchaseModal] = useState(false);
    const [currentCreditBalance, setCurrentCreditBalance] = useState<number | null>(null);

    // Pagination state
    const [transactions, setTransactions] = useState<PaymentTransaction[]>([]);
    const [transactionsLoading, setTransactionsLoading] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [totalTransactions, setTotalTransactions] = useState(0);
    const transactionsPerPage = 10;

    const axiosPrivate = useAxiosPrivate();

    const fetchBillingData = async () => {
        try {
            setLoading(true);
            const dashboardResponse = await fetchWithCache(axiosPrivate, '/api/credits/dashboard');

            const newBillingData = {
                creditBalance: dashboardResponse.data.creditBalance,
                usageAnalytics: dashboardResponse.data.usageAnalytics,
                recentTransactions: [] // We'll fetch transactions separately with pagination
            };
            setBillingData(newBillingData);
            setCurrentCreditBalance(newBillingData.creditBalance.currentBalance);
        } catch (error) {
            console.error('Error fetching billing data:', error);
            toast.error('Failed to load billing data', {
                autoClose: 5000
            });
        } finally {
            setLoading(false);
        }
    };

    const fetchTransactions = async (page: number = 1) => {
        try {
            setTransactionsLoading(true);
            // Filter for only PURCHASE and INITIAL_GRANT transactions
            const response = await fetchWithCache(
                axiosPrivate,
                `/api/credits/transactions?page=${page}&limit=${transactionsPerPage}&types=PURCHASE,INITIAL_GRANT`
            );

            setTransactions(response.data.transactions || []);
            setCurrentPage(response.data.pagination?.page || 1);
            setTotalPages(response.data.pagination?.totalPages || 1);
            setTotalTransactions(response.data.pagination?.total || 0);
        } catch (error) {
            console.error('Error fetching transactions:', error);
            toast.error('Failed to load transactions', {
                autoClose: 5000
            });
        } finally {
            setTransactionsLoading(false);
        }
    };

    useEffect(() => {
        fetchBillingData();
        fetchTransactions(1);
    }, []);

    // Listen for window focus to refresh credit balance when user returns to the page
    useEffect(() => {

        const handleWindowFocus = () => {
            // Only refresh if the page is visible and we have existing data
            if (!document.hidden && billingData) {
                // Just refresh the credit balance, not the entire billing data
                const refreshCreditBalance = async () => {
                    try {
                        const response = await axiosPrivate.get('/api/credits/balance');
                        if (response.data?.data?.currentBalance !== undefined) {
                            const newBalance = response.data.data.currentBalance;
                            setCurrentCreditBalance(newBalance);

                            // Update billing data to reflect the new balance
                            setBillingData(prev => prev ? {
                                ...prev,
                                creditBalance: {
                                    ...prev.creditBalance,
                                    currentBalance: newBalance
                                }
                            } : null);
                        }
                    } catch (error) {
                        console.error('Error refreshing credit balance:', error);
                    }
                };

                refreshCreditBalance();
            }
        };

        window.addEventListener('focus', handleWindowFocus);
        document.addEventListener('visibilitychange', handleWindowFocus);

        return () => {
            window.removeEventListener('focus', handleWindowFocus);
            document.removeEventListener('visibilitychange', handleWindowFocus);
        };
    }, [axiosPrivate, billingData]);

    const handleTransactionClick = (transaction: PaymentTransaction) => {
        if (transaction.payment?.razorpayPaymentId) {
            setSelectedTransaction(transaction);
            setShowReceiptModal(true);
        } else {
            toast.info('Receipt not available for this transaction', {
                autoClose: 5000
            });
        }
    };

    const handlePurchaseSuccess = (creditsAdded: number, newBalance: number) => {
        toast.success(`Successfully purchased ${creditsAdded} credits! New balance: ${newBalance}`, {
            autoClose: 5000
        });

        // Update credit balance in real-time
        setCurrentCreditBalance(newBalance);

        // Update billing data to reflect the new balance
        setBillingData(prev => prev ? {
            ...prev,
            creditBalance: {
                ...prev.creditBalance,
                currentBalance: newBalance,
                totalSpent: prev.creditBalance.totalSpent + creditsAdded
            }
        } : null);

        // Optionally refresh full data to get updated transactions
        fetchBillingData();
    };

    const handleDownloadAll = () => {
        const transactionsWithReceipts = transactions.filter(
            transaction => transaction.payment?.razorpayPaymentId
        ) || [];

        if (transactionsWithReceipts.length === 0) {
            toast.info('No receipts available for download', {
                autoClose: 5000
            });
            return;
        }

        const loadingToast = toast.loading(`Preparing ${transactionsWithReceipts.length} receipts for download...`);

        try {
            transactionsWithReceipts.forEach((transaction, index) => {
                setTimeout(() => {
                    // Calculate correct invoice number for this transaction
                    const transactionIndex = transactions.findIndex(t => t._id === transaction._id);
                    const invoiceNumber = totalTransactions - ((currentPage - 1) * transactionsPerPage) - transactionIndex;

                    const receiptContent = generateReceiptHTML(transaction, invoiceNumber);
                    const printWindow = window.open('', '_blank');
                    if (printWindow) {
                        printWindow.document.write(receiptContent);
                        printWindow.document.close();
                        printWindow.print();
                    }
                }, index * 500); // Stagger the downloads to avoid browser blocking
            });

            setTimeout(() => {
                toast.update(loadingToast, {
                    render: `Successfully prepared ${transactionsWithReceipts.length} receipts for download`,
                    type: 'success',
                    isLoading: false,
                    autoClose: 3000
                });
            }, transactionsWithReceipts.length * 500);

        } catch (error) {
            console.error('Error downloading receipts:', error);
            toast.update(loadingToast, {
                render: 'Error preparing receipts for download',
                type: 'error',
                isLoading: false,
                autoClose: 3000
            });
        }
    };

    const generateReceiptHTML = (transaction: PaymentTransaction, invoiceNumber: number) => {
        return `
            <html>
                <head>
                    <title>Payment Receipt - ${transaction.transactionId}</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 20px; }
                        .logo { font-size: 24px; font-weight: bold; color: #333; }
                        .receipt-title { font-size: 18px; margin-top: 10px; }
                        .section { margin: 20px 0; }
                        .section-title { font-weight: bold; font-size: 16px; margin-bottom: 10px; border-bottom: 1px solid #ccc; padding-bottom: 5px; }
                        .row { display: flex; justify-content: space-between; margin: 8px 0; }
                        .label { font-weight: bold; }
                        .value { text-align: right; }
                        .total { border-top: 2px solid #333; padding-top: 10px; margin-top: 20px; }
                        .footer { text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ccc; color: #666; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <div class="logo">AegisScholar</div>
                        <div class="receipt-title">Payment Receipt</div>
                    </div>

                    <div class="section">
                        <div class="section-title">Transaction Details</div>
                        <div class="row">
                            <span class="label">Invoice #:</span>
                            <span class="value">${String(invoiceNumber).padStart(3, '0')} - ${formatDate(transaction.createdAt)}</span>
                        </div>
                        <div class="row">
                            <span class="label">Transaction ID:</span>
                            <span class="value">${transaction.transactionId}</span>
                        </div>
                        <div class="row">
                            <span class="label">Payment ID:</span>
                            <span class="value">${transaction.payment?.razorpayPaymentId || 'N/A'}</span>
                        </div>
                        <div class="row">
                            <span class="label">Date:</span>
                            <span class="value">${formatDate(transaction.createdAt)}</span>
                        </div>
                        <div class="row">
                            <span class="label">Status:</span>
                            <span class="value">${transaction.status === 'COMPLETED' ? 'Paid' : transaction.status}</span>
                        </div>
                    </div>

                    <div class="section">
                        <div class="section-title">Package Details</div>
                        <div class="row">
                            <span class="label">Package:</span>
                            <span class="value">${transaction.payment?.packageName || 'Credit Package'}</span>
                        </div>
                        <div class="row">
                            <span class="label">Credits:</span>
                            <span class="value">${transaction.creditAmount}</span>
                        </div>
                    </div>

                    <div class="total">
                        <div class="row">
                            <span class="label">Total Amount:</span>
                            <span class="value">${transaction.payment?.amount ? formatAmount(transaction.payment.amount) : 'N/A'}</span>
                        </div>
                    </div>

                    <div class="footer">
                        <p>Thank you for your purchase!</p>
                        <p>For support, contact <NAME_EMAIL></p>
                        <p>Generated on ${new Date().toLocaleDateString('en-IN')}</p>
                    </div>
                </body>
            </html>
        `;
    };

    const formatAmount = (amount: number) => {
        return `₹${amount.toLocaleString()}`;
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-IN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    };

    const getUsagePercentage = () => {
        if (!billingData?.creditBalance) return 0;
        const { totalEarned, totalSpent } = billingData.creditBalance;
        if (totalEarned === 0) return 0;
        return Math.min((totalSpent / totalEarned) * 100, 100);
    };

    if (loading) {
        return (
            <BillingErrorBoundary>
                <div className="flex items-center justify-center h-screen">
                    <div className="max-w-6xl m-auto">
                        <div className="flex flex-col gap-4 items-center justify-center py-12">
                            {/* <ArrowPathIcon className="w-8 h-8 animate-spin text-primary" /> */}
                            <PulsatingDots />
                            <span className="ml-3 text-lg text-muted-foreground">Loading billing data</span>
                        </div>
                    </div>
                </div>
            </BillingErrorBoundary>
        );
    }

    return (
        <BillingErrorBoundary>
            <div className="min-h-screen bg-background p-2 pb-16">
                <div className="space-y-4 pt-2">
                    {/* Header */}
                    <div>
                        <h1 className="text-3xl font-['Space_Grotesk'] font-bold text-foreground">
                            Billing
                        </h1>
                        <p className="text-muted-foreground mt-1">
                            Manage your billing and payment details.
                        </p>
                    </div>

                    {/* Main Content Grid */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        {/* Current Plan Section */}
                        <div className="bg-card rounded-lg border border-border p-6">
                            <div className="flex items-center justify-between mb-4">
                                <div>
                                    <h2 className="text-lg font-semibold text-foreground">Credit Usage</h2>
                                    <p className="text-sm text-muted-foreground">Track your credit consumption</p>
                                </div>
                                <span className="px-3 py-1 bg-primary/10 text-primary text-sm font-medium rounded-full">
                                    Active
                                </span>
                            </div>

                            <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <span className="flex items-center gap-1 text-2xl font-bold text-foreground">
                                        <img src="/money_filled.png" alt="Coin" className="w-5 h-5" />
                                        {billingData?.creditBalance?.currentBalance || 0}
                                    </span>
                                    <span className="text-sm text-muted-foreground">credits remaining</span>
                                </div>

                                <div className="space-y-2">
                                    <div className="flex justify-between text-sm">
                                        <span className="text-muted-foreground">
                                            {billingData?.creditBalance?.totalSpent || 0} of {billingData?.creditBalance?.totalEarned || 0} credits used
                                        </span>
                                    </div>
                                    <div className="w-full bg-muted rounded-full h-2">
                                        <div
                                            className="bg-primary h-2 rounded-full transition-all duration-300"
                                            style={{ width: `${getUsagePercentage()}%` }}
                                        />
                                    </div>
                                </div>

                                <button
                                    onClick={() => setShowPurchaseModal(true)}
                                    className="w-full mt-4 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors flex items-center justify-center gap-2"
                                >
                                    {/* <img src="/money_filled.png" alt="Coin" className="w-6 h-6" /> */}
                                    Purchase Credits
                                </button>
                            </div>
                        </div>

                        {/* Usage Statistics */}
                        <div className="bg-card rounded-lg border border-border p-6">
                            <h2 className="text-lg font-semibold text-foreground mb-4">Usage Statistics</h2>
                            <p className="text-sm text-muted-foreground mb-6">
                                Monitor your platform usage patterns.
                            </p>

                            <div className="space-y-4">
                                <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                                    <div className="flex items-center gap-3">
                                        <div className="p-2 bg-primary/10 rounded-full">
                                            <ChartBarIcon className="w-4 h-4 text-primary" />
                                        </div>
                                        <span className="text-sm text-muted-foreground">Total Evaluations</span>
                                    </div>
                                    <span className="font-semibold text-foreground">
                                        {billingData?.creditBalance.totalSpent || 0}
                                    </span>
                                </div>

                                <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                                    <div className="flex items-center gap-3">
                                        <div className="p-2 bg-success/10 rounded-full">
                                            <CreditCardIcon className="w-4 h-4 text-success" />
                                        </div>
                                        <span className="text-sm text-muted-foreground">Avg. Credits/Day</span>
                                    </div>
                                    <span className="font-semibold text-foreground">
                                        {billingData?.usageAnalytics.averageCreditsPerDay || 0}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Billing and Invoicing Section */}
                    <div className="bg-card rounded-lg border border-border">
                        <div className="p-6 border-b border-border">
                            <div className="flex items-center justify-between">
                                <div>
                                    <h2 className="text-lg font-semibold text-foreground">Billing and invoicing</h2>
                                    <p className="text-sm text-muted-foreground">Pick an account plan that fits your workflow.</p>
                                </div>
                                <div className="flex items-center gap-3">
                                    {/* Pagination Controls */}
                                    {totalPages > 1 && (
                                        <div className="flex items-center gap-2">
                                            <button
                                                onClick={() => fetchTransactions(currentPage - 1)}
                                                disabled={currentPage === 1 || transactionsLoading}
                                                className="px-3 py-1 text-sm border border-border rounded hover:bg-muted disabled:opacity-50 disabled:cursor-not-allowed"
                                            >
                                                Previous
                                            </button>
                                            <span className="text-sm text-muted-foreground">
                                                Page {currentPage} of {totalPages}
                                            </span>
                                            <button
                                                onClick={() => fetchTransactions(currentPage + 1)}
                                                disabled={currentPage === totalPages || transactionsLoading}
                                                className="px-3 py-1 text-sm border border-border rounded hover:bg-muted disabled:opacity-50 disabled:cursor-not-allowed"
                                            >
                                                Next
                                            </button>
                                        </div>
                                    )}
                                    {/* <button
                                        className="text-sm text-muted-foreground border border-border p-2 rounded-lg hover:text-foreground transition-colors flex items-center gap-1"
                                        onClick={() => handleDownloadAll()}
                                    >
                                        <DocumentArrowDownIcon className="w-4 h-4" />
                                        Download all
                                    </button> */}
                                </div>
                            </div>
                        </div>

                        {/* Invoice Table */}
                        <div className="overflow-auto">
                            <table className="w-full">
                                <thead className="bg-muted/30">
                                    <tr>
                                        {/* <th className="text-left p-4 text-sm font-medium text-muted-foreground">
                                            <input type="checkbox" className="rounded border-border" />
                                        </th> */}
                                        <th className="text-left p-4 text-sm font-medium text-muted-foreground">Invoice</th>
                                        <th className="text-left p-4 text-sm font-medium text-muted-foreground">Billing date</th>
                                        <th className="text-left p-4 text-sm font-medium text-muted-foreground">Status</th>
                                        <th className="text-left p-4 text-sm font-medium text-muted-foreground">Credits</th>
                                        <th className="text-left p-4 text-sm font-medium text-muted-foreground">Plan</th>
                                        <th className="text-left p-4 text-sm font-medium text-muted-foreground">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {transactionsLoading ? (
                                        <tr>
                                            <td colSpan={6} className="text-center py-8">
                                                <PulsatingDots />
                                            </td>
                                        </tr>
                                    ) : transactions.length === 0 ? (
                                        <tr>
                                            <td colSpan={6} className="text-center py-8 text-muted-foreground">
                                                No transactions found
                                            </td>
                                        </tr>
                                    ) : (
                                        transactions.map((transaction, index) => {
                                            // Calculate correct invoice number (newest first, so reverse the numbering)
                                            const invoiceNumber = totalTransactions - ((currentPage - 1) * transactionsPerPage) - index;

                                            return (
                                                <tr key={transaction._id} className="border-b border-border hover:bg-muted/20 transition-colors">
                                                    <td className="p-4">
                                                        <div className="flex items-center gap-3">
                                                            {/* <div className="p-2 bg-success/10 rounded">
                                                                <DocumentArrowDownIcon className="w-4 h-4 text-success" />
                                                            </div> */}
                                                            <span className="font-medium text-foreground">
                                                                Invoice #{String(invoiceNumber).padStart(3, '0')} - {formatDate(transaction.createdAt)}
                                                            </span>
                                                        </div>
                                                    </td>
                                                    <td className="p-4 text-muted-foreground">
                                                        {formatDate(transaction.createdAt)}
                                                    </td>
                                                    <td className="p-4">
                                                        <span className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-full ${transaction.status === 'COMPLETED'
                                                            ? 'bg-success/10 text-success'
                                                            : transaction.status === 'PENDING'
                                                                ? 'bg-warning/10 text-warning'
                                                                : 'bg-destructive/10 text-destructive'
                                                            }`}>
                                                            <CheckCircleIcon className="w-3 h-3" />
                                                            {transaction.status === 'COMPLETED' ? 'Paid' : transaction.status}
                                                        </span>
                                                    </td>
                                                    <td className="flex p-4 font-medium text-foreground gap-1">
                                                        <img src="/money_filled.png" alt="Coin" className="w-5 h-5" />
                                                        {transaction.creditAmount}
                                                    </td>
                                                    <td className="p-4 text-muted-foreground">
                                                        {transaction.payment?.packageName || 'Credit Package'}
                                                    </td>
                                                    <td className="p-4 text-right">
                                                        <button
                                                            onClick={() => handleTransactionClick(transaction)}
                                                            className="flex flex-row gap-1 text-primary hover:text-primary/80 text-sm font-medium transition-colors"
                                                            disabled={!transaction.payment?.razorpayPaymentId}
                                                        >
                                                            <DocumentArrowDownIcon className="w-4 h-4" />
                                                            Download
                                                        </button>
                                                    </td>
                                                </tr>
                                            );
                                        })
                                    )}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                {/* Modals */}
                {showReceiptModal && selectedTransaction && (
                    <ReceiptModal
                        isOpen={showReceiptModal}
                        onClose={() => {
                            setShowReceiptModal(false);
                            setSelectedTransaction(null);
                        }}
                        transaction={selectedTransaction}
                        onDownload={() => {
                            toast.success('Receipt downloaded successfully', {
                                autoClose: 5000
                            });
                        }}
                    />
                )}

                <CreditPurchaseModal
                    isOpen={showPurchaseModal}
                    onClose={() => setShowPurchaseModal(false)}
                    onSuccess={handlePurchaseSuccess}
                />
            </div>
        </BillingErrorBoundary>
    );
};

export default BillingDashboard;
