# JSON Evaluation Parser

This module provides a comprehensive parser for handling JSON-based evaluation results from the AegisGrader system. It replaces the previous XML-based parser and is designed to work with the new evaluation prompt template that outputs structured JSON responses.

## Overview

The JSON Evaluation Parser handles evaluation results in the new format where each question evaluation is returned as a JSON object with detailed feedback, marks breakdown, and structured analysis. The parser can handle both single question evaluations and multiple question evaluations.

## Key Features

- **Robust JSON Parsing**: Handles various input formats including raw JSON objects, JSON strings, and arrays
- **Markdown Code Block Extraction**: Automatically extracts JSON from markdown code blocks
- **Comprehensive Feedback Processing**: Converts structured feedback into readable formats
- **Marks Extraction**: Intelligently extracts total possible marks from justification text
- **Validation**: Built-in validation for evaluation data integrity
- **Compatibility**: Maintains compatibility with existing GradingDetails format
- **Error Handling**: Graceful handling of malformed data and edge cases

## Installation

The parser is part of the frontend utilities and can be imported directly:

```typescript
import {
    parseJsonEvaluationData,
    parseEvaluationForGradingDetails,
    validateJsonEvaluationData,
    JsonQuestionEvaluation,
    EvaluationBreakdown
} from './jsonEvaluationParser';
```

## Usage

### Basic Usage

```typescript
import { parseJsonEvaluationData } from './jsonEvaluationParser';

// Parse a single question evaluation
const evaluationData: JsonQuestionEvaluation = {
    questionNumber: "1",
    subject: "History",
    feedback: {
        marks_awarded: {
            score: 18,
            justification: "The score of 18 out of 25 is assigned."
        },
        overall_assessment: "Excellent answer with good analysis.",
        paragraph_by_paragraph_analysis: [],
        positive_observations: ["Strong analytical skills."],
        critical_deficiencies: [],
        prescriptive_actions_for_rectification: "Continue with current approach."
    }
};

const result = parseJsonEvaluationData(evaluationData);

if (result) {
    console.log(`Total Marks: ${result.totalMarks}/${result.maxMarks}`);
    console.log(`Percentage: ${result.overallPercentage}%`);
    console.log(`Questions: ${result.questions.length}`);
}
```

### Multiple Question Evaluations

```typescript
// Parse multiple question evaluations
const multipleQuestionsData: JsonQuestionEvaluation[] = [
    // ... array of question evaluations
];

const result = parseJsonEvaluationData(multipleQuestionsData);
```

### Handling Different Input Formats

The parser can handle various input formats:

```typescript
// Raw JSON object
const result1 = parseJsonEvaluationData(jsonObject);

// JSON string
const result2 = parseJsonEvaluationData(jsonString);

// JSON wrapped in markdown code blocks
const result3 = parseJsonEvaluationData("```json\n{...}\n```");

// Array of evaluations
const result4 = parseJsonEvaluationData([eval1, eval2, eval3]);
```

### Validation

```typescript
import { validateJsonEvaluationData } from './jsonEvaluationParser';

const validationResult = validateJsonEvaluationData(evaluationData);

if (validationResult.isValid) {
    console.log('Evaluation data is valid');
} else {
    console.log('Errors:', validationResult.errors);
    console.log('Warnings:', validationResult.warnings);
}
```

### GradingDetails Compatibility

```typescript
import { parseEvaluationForGradingDetails } from './jsonEvaluationParser';

const gradingDetailsResult = parseEvaluationForGradingDetails(evaluationData);

if (gradingDetailsResult) {
    console.log(`Total Marks: ${gradingDetailsResult.total_marks}`);
    console.log(`Max Marks: ${gradingDetailsResult.maximum_possible_marks}`);
    console.log(`Percentage: ${gradingDetailsResult.percentage_score}%`);
}
```

## Data Structures

### Input Format (JsonQuestionEvaluation)

```typescript
interface JsonQuestionEvaluation {
    questionNumber: string;
    subject: string;
    feedback: {
        marks_awarded: {
            score: number | string; // Can be number (legacy) or string like "5/10"
            justification: string;
        };
        overall_assessment: string;
        paragraph_by_paragraph_analysis: Array<{
            section_title: string;
            student_text_snippet: string;
            analysis_and_feedback: string;
        }>;
        positive_observations: string[];
        critical_deficiencies: string[];
        prescriptive_actions_for_rectification: string;
    };
}
```

### Output Format (EvaluationBreakdown)

```typescript
interface EvaluationBreakdown {
    totalMarks: number;
    maxMarks: number;
    overallPercentage: number;
    questions: Array<{
        questionNumber: string;
        marksAwarded: number;
        marksPossible: number;
        percentage: number;
        feedback: string;
        structuredFeedback?: FeedbackSection[];
        criteriaBreakdown: CriterionBreakdown[];
    }>;
}
```

## Edge Cases Handled

### 1. "Not Attempted" Responses

The parser correctly handles questions that were not attempted:

```json
{
    "questionNumber": "1",
    "subject": "History",
    "feedback": {
        "marks_awarded": {
            "score": 0,
            "justification": "Question not attempted by the candidate."
        },
        "overall_assessment": "Not Attempted.",
        "paragraph_by_paragraph_analysis": [],
        "positive_observations": [],
        "critical_deficiencies": [],
        "prescriptive_actions_for_rectification": "No action required as the question was not attempted."
    }
}
```

### 2. Error Responses

The parser handles error responses gracefully:

```json
{
    "error": "Malformed input data provided. Cannot proceed with evaluation."
}
```

### 3. Markdown Code Blocks

The parser automatically extracts JSON from markdown code blocks:

```markdown
```json
{
    "questionNumber": "1",
    "subject": "History",
    "feedback": { ... }
}
```
```

### 4. Score Format Support

The parser supports multiple score formats:

#### New Format (Recommended)
```json
{
    "marks_awarded": {
        "score": "5/10",
        "justification": "Good answer with room for improvement."
    }
}
```

#### Legacy Format (Still Supported)
```json
{
    "marks_awarded": {
        "score": 5,
        "justification": "The score of 5 out of 10 is assigned..."
    }
}
```

### 5. Marks Extraction

The parser intelligently extracts total possible marks from justification text using various patterns:

- "X out of Y"
- "X/Y"
- "score of X out of Y"
- "X marks out of Y"

## Utility Functions

### Calculation Helpers

```typescript
import {
    calculateTotalMarksFromQuestions,
    calculateTotalPossibleMarksFromQuestions,
    validateAndCorrectEvaluationTotals
} from './jsonEvaluationParser';

// Calculate totals from question breakdowns
const totalMarks = calculateTotalMarksFromQuestions(questions);
const maxMarks = calculateTotalPossibleMarksFromQuestions(questions);

// Validate and correct evaluation totals
const correctedData = validateAndCorrectEvaluationTotals(evaluationData);
```

### Sanitization Functions

```typescript
import {
    sanitizeNumber,
    sanitizeString,
    sanitizePercentage
} from './jsonEvaluationParser';

// Sanitize various data types
const cleanNumber = sanitizeNumber(rawValue, fallback);
const cleanString = sanitizeString(rawValue, fallback);
const percentage = sanitizePercentage(awarded, possible);
```

## Testing

The parser includes comprehensive tests covering:

- Single question evaluations
- Multiple question evaluations
- Edge cases (not attempted, errors)
- Different input formats
- Validation scenarios
- Calculation helpers

Run tests with:

```bash
npm test jsonEvaluationParser.test.ts
```

## Migration from XML Parser

If you're migrating from the XML parser, the main changes are:

1. **Import Change**: Use `jsonEvaluationParser` instead of `xmlEvaluationParser`
2. **Function Names**: Most function names remain the same for compatibility
3. **Data Format**: Input format changes from XML to JSON
4. **Output Format**: Output format remains the same for compatibility

### Migration Example

```typescript
// Old XML parser usage
import { parseEvaluationData } from './xmlEvaluationParser';
const result = parseEvaluationData(xmlData);

// New JSON parser usage
import { parseJsonEvaluationData } from './jsonEvaluationParser';
const result = parseJsonEvaluationData(jsonData);
```

## Error Handling

The parser provides comprehensive error handling:

- **Null Returns**: Returns `null` for invalid or malformed data
- **Console Logging**: Detailed error messages for debugging
- **Validation**: Built-in validation with detailed error and warning messages
- **Graceful Degradation**: Continues processing even if some questions fail

## Performance Considerations

- **Efficient Parsing**: Uses native JSON parsing for optimal performance
- **Memory Efficient**: Processes data in streams where possible
- **Caching**: No built-in caching, but can be easily added if needed
- **Validation**: Validation is optional and can be skipped for performance-critical scenarios

## Contributing

When contributing to the JSON evaluation parser:

1. **Add Tests**: Always add tests for new functionality
2. **Update Documentation**: Keep this README updated
3. **Maintain Compatibility**: Ensure backward compatibility with existing interfaces
4. **Error Handling**: Add appropriate error handling for new features
5. **Validation**: Include validation for new data structures

## Examples

See `jsonEvaluationParserExample.ts` for comprehensive usage examples covering all scenarios. 