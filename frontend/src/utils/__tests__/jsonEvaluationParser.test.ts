import {
    parseJsonEvaluationData,
    parseEvaluationForGradingDetails,
    validateJsonEvaluationData,
    calculateTotalMarksFromQuestions,
    calculateTotalPossibleMarksFromQuestions,
    validateAndCorrectEvaluationTotals,
    JsonQuestionEvaluation,
    EvaluationBreakdown,
    ParsedEvaluation,
    formatAnalysisAndFeedback,
    formatAnalysisAndFeedbackWithLineBreaks
} from '../jsonEvaluationParser';

describe('JSON Evaluation Parser', () => {
    describe('parseJsonEvaluationData', () => {
        it('should parse a single question evaluation', () => {
            const singleQuestionData: JsonQuestionEvaluation = {
                questionNumber: "1",
                subject: "History",
                feedback: {
                    marks_awarded: {
                        score: 15,
                        justification: "The score of 15 out of 25 is assigned. While factually competent, the answer's failure to move beyond description to the required critical analysis, coupled with a lack of consistent evidentiary support, fundamentally limits its merit."
                    },
                    overall_assessment: "This answer presents a factually sound but analytically underdeveloped treatment of the topic. The candidate demonstrates knowledge, but not the ability to weave it into a nuanced, evidence-backed argument.",
                    paragraph_by_paragraph_analysis: [
                        {
                            section_title: "Introduction",
                            paragraph_number: 1,
                            student_text_snippet: "The French Revolution was a period of radical social and political upheaval.",
                            core_claim: "The French Revolution was a significant historical event",
                            evidence_present: "No specific evidence provided",
                            reasoning_quality: "Basic description without analysis",
                            link_to_question: "Sets up the topic but doesn't answer the directive",
                            analysis_notes: "The introduction correctly identifies the topic but fails to advance a thesis. An effective introduction must propose a specific line of argument.",
                            specific_improvements: ["Add a clear thesis statement", "Provide a roadmap for the argument"],
                            evidence_to_add: "Include specific dates, events, or primary sources",
                            structural_fix: "Add a thesis sentence and outline the argument structure"
                        },
                        {
                            section_title: "Body Paragraph 1",
                            paragraph_number: 2,
                            student_text_snippet: "The revolution began in 1789 with the storming of the Bastille.",
                            core_claim: "The Bastille event marked the beginning of the revolution",
                            evidence_present: "Basic date provided but no detailed evidence",
                            reasoning_quality: "Factual statement without causal analysis",
                            link_to_question: "Provides historical context but lacks analytical depth",
                            analysis_notes: "This paragraph correctly states historical facts but presents them as self-evident. A Mains-level argument requires substantiation.",
                            specific_improvements: ["Analyze why the Bastille was significant", "Connect to broader revolutionary themes"],
                            evidence_to_add: "Include eyewitness accounts or contemporary reactions",
                            structural_fix: "Add analysis connecting this event to the revolution's causes"
                        }
                    ],
                    positive_observations: [
                        "Command of historical facts was precise and accurate.",
                        "The handwriting is legible and the presentation is clean."
                    ],
                    critical_deficiencies: [
                        "Analysis is consistently descriptive rather than evaluative.",
                        "The argument lacks a cohesive structure."
                    ],
                    prescriptive_actions_for_rectification: "To achieve the required standard, future answers must be built on the following principles: 1. Argument over Assertion 2. Evidence as Foundation 3. Analysis over Description."
                }
            };

            const result = parseJsonEvaluationData(singleQuestionData);

            expect(result).not.toBeNull();
            expect(result?.totalMarks).toBe(15);
            expect(result?.maxMarks).toBe(25);
            expect(result?.overallPercentage).toBe(60);
            expect(result?.questions).toHaveLength(1);
            expect(result?.questions[0].questionNumber).toBe("1");
            expect(result?.questions[0].marksAwarded).toBe(15);
            expect(result?.questions[0].marksPossible).toBe(25);
            expect(result?.questions[0].feedback).toContain("Overall Assessment:");
            expect(result?.questions[0].feedback).toContain("Detailed Analysis:");
            expect(result?.questions[0].feedback).toContain("Positive Observations:");
            expect(result?.questions[0].feedback).toContain("Critical Deficiencies:");
            expect(result?.questions[0].feedback).toContain("Recommendations:");
        });

        it('should parse multiple question evaluations', () => {
            const multipleQuestionsData: JsonQuestionEvaluation[] = [
                {
                    questionNumber: "1",
                    subject: "History",
                    feedback: {
                        marks_awarded: {
                            score: 15,
                            justification: "The score of 15 out of 25 is assigned."
                        },
                        overall_assessment: "This answer presents a factually sound treatment.",
                        paragraph_by_paragraph_analysis: [],
                        positive_observations: ["Good factual knowledge."],
                        critical_deficiencies: ["Lacks analytical depth."],
                        prescriptive_actions_for_rectification: "Need more analysis."
                    }
                },
                {
                    questionNumber: "2",
                    subject: "History",
                    feedback: {
                        marks_awarded: {
                            score: 18,
                            justification: "The score of 18 out of 25 is assigned."
                        },
                        overall_assessment: "This answer shows good analytical skills.",
                        paragraph_by_paragraph_analysis: [],
                        positive_observations: ["Excellent analysis."],
                        critical_deficiencies: ["Some factual errors."],
                        prescriptive_actions_for_rectification: "Improve factual accuracy."
                    }
                }
            ];

            const result = parseJsonEvaluationData(multipleQuestionsData);

            expect(result).not.toBeNull();
            expect(result?.totalMarks).toBe(33);
            expect(result?.maxMarks).toBe(50);
            expect(result?.overallPercentage).toBe(66);
            expect(result?.questions).toHaveLength(2);
            expect(result?.questions[0].questionNumber).toBe("1");
            expect(result?.questions[1].questionNumber).toBe("2");
        });

        it('should handle "Not Attempted" responses', () => {
            const notAttemptedData: JsonQuestionEvaluation = {
                questionNumber: "1",
                subject: "History",
                feedback: {
                    marks_awarded: {
                        score: 0,
                        justification: "Question not attempted by the candidate."
                    },
                    overall_assessment: "Not Attempted.",
                    paragraph_by_paragraph_analysis: [],
                    positive_observations: [],
                    critical_deficiencies: [],
                    prescriptive_actions_for_rectification: "No action required as the question was not attempted."
                }
            };

            const result = parseJsonEvaluationData(notAttemptedData);

            expect(result).not.toBeNull();
            expect(result?.totalMarks).toBe(0);
            expect(result?.questions[0].marksAwarded).toBe(0);
            expect(result?.questions[0].feedback).toContain("Not Attempted");
        });

        it('should handle error responses', () => {
            const errorData = {
                error: "Malformed input data provided. Cannot proceed with evaluation."
            };

            const result = parseJsonEvaluationData(errorData);

            expect(result).toBeNull();
        });

        it('should handle JSON strings wrapped in markdown', () => {
            const jsonString = `\`\`\`json
{
    "questionNumber": "1",
    "subject": "History",
    "feedback": {
        "marks_awarded": {
            "score": 20,
            "justification": "The score of 20 out of 25 is assigned."
        },
        "overall_assessment": "Excellent answer with good analysis.",
        "paragraph_by_paragraph_analysis": [],
        "positive_observations": ["Strong analytical skills."],
        "critical_deficiencies": [],
        "prescriptive_actions_for_rectification": "Continue with current approach."
    }
}
\`\`\``;

            const result = parseJsonEvaluationData(jsonString);

            expect(result).not.toBeNull();
            expect(result?.totalMarks).toBe(20);
            expect(result?.maxMarks).toBe(25);
            expect(result?.questions[0].questionNumber).toBe("1");
        });

        it('should extract marks possible from justification text', () => {
            const dataWithJustification: JsonQuestionEvaluation = {
                questionNumber: "1",
                subject: "History",
                feedback: {
                    marks_awarded: {
                        score: 12,
                        justification: "The score of 12 out of 20 is assigned due to lack of critical analysis."
                    },
                    overall_assessment: "Basic answer with some factual accuracy.",
                    paragraph_by_paragraph_analysis: [],
                    positive_observations: ["Some correct facts."],
                    critical_deficiencies: ["Lacks depth."],
                    prescriptive_actions_for_rectification: "Need more analysis."
                }
            };

            const result = parseJsonEvaluationData(dataWithJustification);

            expect(result).not.toBeNull();
            expect(result?.maxMarks).toBe(20);
            expect(result?.questions[0].marksPossible).toBe(20);
        });
    });

    describe('parseEvaluationForGradingDetails', () => {
        it('should convert to GradingDetails format', () => {
            const singleQuestionData: JsonQuestionEvaluation = {
                questionNumber: "1",
                subject: "History",
                feedback: {
                    marks_awarded: {
                        score: 15,
                        justification: "The score of 15 out of 25 is assigned."
                    },
                    overall_assessment: "Good answer with room for improvement.",
                    paragraph_by_paragraph_analysis: [],
                    positive_observations: ["Good knowledge."],
                    critical_deficiencies: ["Needs more analysis."],
                    prescriptive_actions_for_rectification: "Improve analytical skills."
                }
            };

            const result = parseEvaluationForGradingDetails(singleQuestionData);

            expect(result).not.toBeNull();
            expect(result?.total_marks).toBe(15);
            expect(result?.maximum_possible_marks).toBe(25);
            expect(result?.percentage_score).toBe(60);
            expect(result?.section).toHaveLength(1);
            expect(result?.section[0].name).toBe("Main Section");
            expect(result?.section[0].section_marks).toBe(15);
            expect(result?.section[0].section_possible_marks).toBe(25);
            expect(result?.section[0].question).toHaveLength(1);
            expect(result?.section[0].question[0].question_number).toBe(1);
            expect(result?.section[0].question[0].marks_awarded).toBe(15);
            expect(result?.section[0].question[0].marks_possible).toBe(25);
        });
    });

    describe('validateJsonEvaluationData', () => {
        it('should validate correct evaluation data', () => {
            const validData: JsonQuestionEvaluation = {
                questionNumber: "1",
                subject: "History",
                feedback: {
                    marks_awarded: {
                        score: 15,
                        justification: "The score of 15 out of 25 is assigned."
                    },
                    overall_assessment: "Good answer.",
                    paragraph_by_paragraph_analysis: [],
                    positive_observations: ["Good knowledge."],
                    critical_deficiencies: ["Needs improvement."],
                    prescriptive_actions_for_rectification: "Improve skills."
                }
            };

            const result = validateJsonEvaluationData(validData);

            expect(result.isValid).toBe(true);
            expect(result.errors).toHaveLength(0);
        });

        it('should detect invalid data', () => {
            const invalidData = null;

            const result = validateJsonEvaluationData(invalidData);

            expect(result.isValid).toBe(false);
            expect(result.errors).toHaveLength(1);
            expect(result.errors[0]).toContain("No evaluation data provided");
        });

        it('should provide warnings for incomplete data', () => {
            const incompleteData: JsonQuestionEvaluation = {
                questionNumber: "",
                subject: "History",
                feedback: {
                    marks_awarded: {
                        score: 0,
                        justification: "No marks awarded."
                    },
                    overall_assessment: "",
                    paragraph_by_paragraph_analysis: [],
                    positive_observations: [],
                    critical_deficiencies: [],
                    prescriptive_actions_for_rectification: ""
                }
            };

            const result = validateJsonEvaluationData(incompleteData);

            expect(result.isValid).toBe(true);
            expect(result.warnings.length).toBeGreaterThan(0);
        });
    });

    describe('Calculation Helper Functions', () => {
        it('should calculate total marks from questions', () => {
            const questions = [
                { questionNumber: "1", marksAwarded: 15, marksPossible: 25, percentage: 60, feedback: "", criteriaBreakdown: [] },
                { questionNumber: "2", marksAwarded: 18, marksPossible: 25, percentage: 72, feedback: "", criteriaBreakdown: [] }
            ];

            const totalMarks = calculateTotalMarksFromQuestions(questions);

            expect(totalMarks).toBe(33);
        });

        it('should calculate total possible marks from questions', () => {
            const questions = [
                { questionNumber: "1", marksAwarded: 15, marksPossible: 25, percentage: 60, feedback: "", criteriaBreakdown: [] },
                { questionNumber: "2", marksAwarded: 18, marksPossible: 25, percentage: 72, feedback: "", criteriaBreakdown: [] }
            ];

            const totalPossibleMarks = calculateTotalPossibleMarksFromQuestions(questions);

            expect(totalPossibleMarks).toBe(50);
        });

        it('should validate and correct evaluation totals', () => {
            const evaluationData: EvaluationBreakdown = {
                totalMarks: 30, // Incorrect total
                maxMarks: 45,   // Incorrect total
                overallPercentage: 67,
                questions: [
                    { questionNumber: "1", marksAwarded: 15, marksPossible: 25, percentage: 60, feedback: "", criteriaBreakdown: [] },
                    { questionNumber: "2", marksAwarded: 18, marksPossible: 25, percentage: 72, feedback: "", criteriaBreakdown: [] }
                ]
            };

            const correctedData = validateAndCorrectEvaluationTotals(evaluationData);

            expect(correctedData.totalMarks).toBe(33);
            expect(correctedData.maxMarks).toBe(50);
            expect(correctedData.overallPercentage).toBe(66);
        });
    });

    describe('Edge Cases', () => {
        it('should handle empty arrays', () => {
            const result = parseJsonEvaluationData([]);
            expect(result).toBeNull();
        });

        it('should handle malformed JSON strings', () => {
            const malformedJson = '{ "questionNumber": "1", "feedback": { "marks_awarded": { "score": 15 } }';
            const result = parseJsonEvaluationData(malformedJson);
            expect(result).toBeNull();
        });

        it('should handle missing required fields', () => {
            const incompleteData = {
                questionNumber: "1",
                // Missing subject and feedback
            };

            const result = parseJsonEvaluationData(incompleteData);
            expect(result).toBeNull();
        });

        it('should handle different mark extraction patterns', () => {
            const patterns = [
                {
                    justification: "Score of 15 out of 25",
                    expected: 25
                },
                {
                    justification: "15/25 marks awarded",
                    expected: 25
                },
                {
                    justification: "15 marks out of 25",
                    expected: 25
                },
                {
                    justification: "Justified score of 15",
                    expected: 0 // No pattern match
                }
            ];

            patterns.forEach(({ justification, expected }) => {
                const data: JsonQuestionEvaluation = {
                    questionNumber: "1",
                    subject: "History",
                    feedback: {
                        marks_awarded: {
                            score: 15,
                            justification
                        },
                        overall_assessment: "Test",
                        paragraph_by_paragraph_analysis: [],
                        positive_observations: [],
                        critical_deficiencies: [],
                        prescriptive_actions_for_rectification: ""
                    }
                };

                const result = parseJsonEvaluationData(data);
                expect(result?.maxMarks).toBe(expected);
            });
        });
    });
});

describe('formatAnalysisAndFeedback', () => {
  it('should separate analysis and specific improvement sections', () => {
    const testContent = "This is the analysis text. Specific improvement: Add a thesis statement that clearly outlines the argument.";
    
    const result = formatAnalysisAndFeedback(testContent);
    
    expect(result.analysis).toBe("This is the analysis text.");
    expect(result.specificImprovement).toBe("Specific improvement: Add a thesis statement that clearly outlines the argument.");
  });

  it('should handle content without specific improvement section', () => {
    const testContent = "This is just analysis text without any specific improvement section.";
    
    const result = formatAnalysisAndFeedback(testContent);
    
    expect(result.analysis).toBe("This is just analysis text without any specific improvement section.");
    expect(result.specificImprovement).toBe("");
  });

  it('should handle case-insensitive matching', () => {
    const testContent = "Analysis text. SPECIFIC IMPROVEMENT: Add more evidence.";
    
    const result = formatAnalysisAndFeedback(testContent);
    
    expect(result.analysis).toBe("Analysis text.");
    expect(result.specificImprovement).toBe("SPECIFIC IMPROVEMENT: Add more evidence.");
  });

  it('should handle empty content', () => {
    const result = formatAnalysisAndFeedback("");
    
    expect(result.analysis).toBe("");
    expect(result.specificImprovement).toBe("");
  });
});

describe('formatAnalysisAndFeedbackWithLineBreaks', () => {
  it('should format content with line breaks and bold headings', () => {
    const testContent = "This is the analysis text. Specific improvement: Add a thesis statement that clearly outlines the argument.";
    
    const result = formatAnalysisAndFeedbackWithLineBreaks(testContent);
    
    expect(result).toBe("This is the analysis text.\n\n**Specific improvement: Add a thesis statement that clearly outlines the argument.**");
  });

  it('should handle content without specific improvement section', () => {
    const testContent = "This is just analysis text without any specific improvement section.";
    
    const result = formatAnalysisAndFeedbackWithLineBreaks(testContent);
    
    expect(result).toBe("This is just analysis text without any specific improvement section.");
  });

  it('should handle case-insensitive matching', () => {
    const testContent = "Analysis text. SPECIFIC IMPROVEMENT: Add more evidence.";
    
    const result = formatAnalysisAndFeedbackWithLineBreaks(testContent);
    
    expect(result).toBe("Analysis text.\n\n**SPECIFIC IMPROVEMENT: Add more evidence.**");
  });

  it('should handle empty content', () => {
    const result = formatAnalysisAndFeedbackWithLineBreaks("");
    
    expect(result).toBe("");
  });
}); 