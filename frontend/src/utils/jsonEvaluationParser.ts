// --- Type Definitions for JSON Evaluation Parser ---

export interface BoundingBox {
    x: number;
    y: number;
    width: number;
    height: number;
    page: number;
}

export interface Paragraph {
    text: string;
    boundingBox: BoundingBox;
}

// Updated interface for the new UPSC evaluation structure
export interface ParagraphAnalysis {
    section_title: string;
    paragraph_number: number;
    student_text_snippet: string;
    core_claim: string;
    evidence_present: string;
    reasoning_quality: string;
    link_to_question: string;
    analysis_notes: string;
    specific_improvements: string[];
    evidence_to_add: string;
    structural_fix: string;
    bounding_box?: BoundingBox;
}

export interface AnswerData {
    questionNumber: string;
    answerText: string;
    paragraphs?: Paragraph[];
}

export interface MarksAwarded {
    score: string; // Format: "X/Y" (e.g., "5/10")
    justification: string;
    marking_rigor_notes?: string; // New field for UPSC evaluation
}

export interface QuestionFeedback {
    marks_awarded: MarksAwarded;
    overall_assessment: string;
    paragraph_by_paragraph_analysis: ParagraphAnalysis[];
    positive_observations: string[];
    critical_deficiencies: string[];
    prescriptive_actions_for_rectification: string;
}

export interface JsonQuestionEvaluation {
    questionNumber: string;
    subject: string;
    feedback: QuestionFeedback;
    paragraphs?: Paragraph[]; // Paragraphs with bounding boxes from answer validation
    error?: string; // Error message if evaluation failed
}

export interface JsonEvaluationError {
    error: string;
}

// Reuse existing interfaces from XML parser for compatibility
export interface CriterionBreakdown {
    criterion: string;
    score: string;
    maxScore?: string;
}

export interface FeedbackSection {
    title: string;
    content: string;
    subsections?: { 
        title: string; 
        content: string; 
        bounding_box?: BoundingBox;
        subsections?: { title: string; content: string; bounding_box?: BoundingBox }[];
    }[];
}

export interface QuestionBreakdown {
    questionNumber: string;
    marksAwarded: number;
    marksPossible: number;
    percentage: number;
    feedback: string;
    structuredFeedback?: FeedbackSection[];
    criteriaBreakdown: CriterionBreakdown[];
    paragraphs?: Paragraph[];
}

export interface EvaluationBreakdown {
    totalMarks: number;
    maxMarks: number;
    overallPercentage: number;
    questions: QuestionBreakdown[];
}

// For GradingDetails compatibility
export interface ParsedEvaluation {
    total_marks: number;
    maximum_possible_marks: number;
    percentage_score: number;
    section: Array<{
        name: string;
        section_marks: number;
        section_possible_marks: number;
        question: Array<{
            question_number: number;
            marks_awarded: number;
            marks_possible: number;
            feedback: string;
        }>;
    }>;
}

// --- Utility Functions ---
const sanitizeNumber = (value: any, fallback: number = 0): number => {
    if (typeof value === 'number' && !isNaN(value) && isFinite(value)) {
        return Math.max(0, Math.round(value));
    }
    if (typeof value === 'string') {
        const parsed = parseFloat(value.replace(/[^\d.-]/g, ''));
        if (!isNaN(parsed) && isFinite(parsed)) {
            return Math.max(0, Math.round(parsed));
        }
    }
    return fallback;
};

const sanitizeString = (value: any, fallback: string = ''): string => {
    if (typeof value === 'string') {
        return value.trim();
    }
    if (value != null) {
        return String(value).trim();
    }
    return fallback;
};

const sanitizePercentage = (awarded: number, possible: number): number => {
    if (possible <= 0) return 0;
    const percentage = (awarded / possible) * 100;
    return Math.max(0, Math.min(100, Math.round(percentage)));
};

// --- JSON Processing Functions ---
const extractJsonFromRawData = (rawData: any): any | null => {
    try {
        let jsonData: any = null;

        // Handle different input formats
        if (Array.isArray(rawData)) {
            if (rawData.length === 0) {
                console.error('Empty array provided for evaluation data');
                return null;
            }
            jsonData = rawData[0];
        } else if (typeof rawData === 'string') {
            try {
                jsonData = JSON.parse(rawData);
            } catch (parseError) {
                console.error('Failed to parse JSON string:', parseError);
                return null;
            }
        } else if (rawData && typeof rawData === 'object') {
            jsonData = rawData;
        } else {
            console.error('Invalid evaluation data format:', typeof rawData, rawData);
            return null;
        }

        if (!jsonData) {
            console.error('Empty JSON data after extraction');
            return null;
        }

        return jsonData;
    } catch (error) {
        console.error('Error extracting JSON from raw data:', error);
        return null;
    }
};

const cleanAndValidateJson = (jsonData: any): any => {
    try {
        // Handle common edge cases from AI responses
        let cleanedData = jsonData;

        // If the data is wrapped in markdown code blocks, extract the JSON
        if (typeof cleanedData === 'string') {
            // Remove markdown code blocks
            cleanedData = cleanedData.replace(/```(?:json)?\s*\n?/gi, '');
            cleanedData = cleanedData.replace(/```\s*$/g, '');
            
            // Try to parse as JSON
            try {
                cleanedData = JSON.parse(cleanedData);
            } catch (parseError) {
                console.error('Failed to parse cleaned JSON string:', parseError);
                // Try to fix common JSON issues
                try {
                    // Fix trailing commas
                    cleanedData = cleanedData.replace(/,(\s*[}\]])/g, '$1');
                    // Fix missing quotes around property names
                    cleanedData = cleanedData.replace(/(\s*)(\w+)(\s*):/g, '$1"$2"$3:');
                    // Fix single quotes to double quotes
                    cleanedData = cleanedData.replace(/'/g, '"');
                    cleanedData = JSON.parse(cleanedData);
                } catch (secondParseError) {
                    console.error('Failed to parse even after fixing common issues:', secondParseError);
                    return null;
                }
            }
        }

        // Validate that we have the expected structure
        if (!cleanedData || typeof cleanedData !== 'object') {
            console.error('Invalid JSON structure: not an object');
            return null;
        }

        // Check for error response
        if (cleanedData.error) {
            console.error('Evaluation error:', cleanedData.error);
            // Instead of returning null, return the error object so it can be handled gracefully
            return cleanedData;
        }

        return cleanedData;
    } catch (error) {
        console.error('Error cleaning and validating JSON:', error);
        return null;
    }
};

// --- Main Parsing Functions ---
export const parseJsonEvaluationData = (rawData: any): EvaluationBreakdown | null => {
    try {
        // Step 1: Extract JSON data from raw data
        const jsonData = extractJsonFromRawData(rawData);
        if (!jsonData) {
            return null;
        }

        // Step 2: Clean and validate JSON
        const cleanedData = cleanAndValidateJson(jsonData);
        if (!cleanedData) {
            return null;
        }

        // Step 3: Handle different response formats
        if (cleanedData.error) {
            // Handle error response gracefully
            console.warn('Evaluation contains error:', cleanedData.error);
            return {
                totalMarks: 0,
                maxMarks: 0,
                overallPercentage: 0,
                questions: [{
                    questionNumber: 'Error',
                    marksAwarded: 0,
                    marksPossible: 0,
                    percentage: 0,
                    feedback: `Evaluation Error: ${cleanedData.error}`,
                    criteriaBreakdown: []
                }]
            };
        } else if (Array.isArray(cleanedData)) {
            // Multiple question evaluations
            return parseMultipleQuestionEvaluations(cleanedData);
        } else if (cleanedData.questionNumber) {
            // Single question evaluation
            return parseSingleQuestionEvaluation(cleanedData);
        } else {
            console.error('Unexpected JSON structure:', cleanedData);
            return null;
        }

    } catch (error) {
        console.error('Error parsing JSON evaluation data:', error);
        return null;
    }
};

const parseSingleQuestionEvaluation = (questionData: JsonQuestionEvaluation): EvaluationBreakdown | null => {
    try {
        const questionBreakdown = convertJsonQuestionToBreakdown(questionData);
        if (!questionBreakdown) {
            return null;
        }

        return {
            totalMarks: questionBreakdown.marksAwarded,
            maxMarks: questionBreakdown.marksPossible,
            overallPercentage: questionBreakdown.percentage,
            questions: [questionBreakdown]
        };

    } catch (error) {
        console.error('Error parsing single question evaluation:', error);
        return null;
    }
};

const parseMultipleQuestionEvaluations = (questionsData: JsonQuestionEvaluation[]): EvaluationBreakdown | null => {
    try {
        const questionBreakdowns: QuestionBreakdown[] = [];

        questionsData.forEach((questionData, index) => {
            try {
                // Check if this question has an error
                if (questionData.error) {
                    console.warn(`Question ${index + 1} has error:`, questionData.error);
                    questionBreakdowns.push({
                        questionNumber: `Question ${index + 1}`,
                        marksAwarded: 0,
                        marksPossible: 0,
                        percentage: 0,
                        feedback: `Error: ${questionData.error}`,
                        criteriaBreakdown: []
                    });
                } else {
                    const questionBreakdown = convertJsonQuestionToBreakdown(questionData);
                    if (questionBreakdown) {
                        questionBreakdowns.push(questionBreakdown);
                    }
                }
            } catch (error) {
                console.warn(`Error parsing question ${index + 1}:`, error);
                // Add error question to maintain structure
                questionBreakdowns.push({
                    questionNumber: `Question ${index + 1}`,
                    marksAwarded: 0,
                    marksPossible: 0,
                    percentage: 0,
                    feedback: `Parsing Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
                    criteriaBreakdown: []
                });
            }
        });

        if (questionBreakdowns.length === 0) {
            console.error('No valid questions found in evaluation data');
            return null;
        }

        // Calculate totals
        const totalMarks = questionBreakdowns.reduce((sum, q) => sum + q.marksAwarded, 0);
        const maxMarks = questionBreakdowns.reduce((sum, q) => sum + q.marksPossible, 0);
        const overallPercentage = sanitizePercentage(totalMarks, maxMarks);

        return {
            totalMarks,
            maxMarks,
            overallPercentage,
            questions: questionBreakdowns
        };

    } catch (error) {
        console.error('Error parsing multiple question evaluations:', error);
        return null;
    }
};

const convertJsonQuestionToBreakdown = (questionData: JsonQuestionEvaluation): QuestionBreakdown | null => {
    try {
        // Extract basic question information
        const questionNumber = sanitizeString(questionData.questionNumber);
        const subject = sanitizeString(questionData.subject);

        if (!questionNumber) {
            console.error('Missing question number in evaluation data');
            return null;
        }

        // Extract marks information - handle the new "X/Y" format
        const scoreData = questionData.feedback?.marks_awarded?.score;
        let marksAwarded = 0;
        let marksPossible = 0;

        if (typeof scoreData === 'string' && scoreData.includes('/')) {
            // Handle "X/Y" format (e.g., "5/10")
            const [awarded, possible] = scoreData.split('/').map((s: string) => sanitizeNumber(s.trim(), 0));
            marksAwarded = awarded;
            marksPossible = possible;
        } else {
            // Handle legacy number format
            marksAwarded = sanitizeNumber(scoreData, 0);
            marksPossible = extractMarksPossibleFromJustification(questionData.feedback?.marks_awarded?.justification);
        }

        const percentage = sanitizePercentage(marksAwarded, marksPossible);

        // Build comprehensive feedback
        const feedback = buildComprehensiveFeedback(questionData.feedback);

        // Create structured feedback sections
        const structuredFeedback = buildStructuredFeedback(questionData.feedback);

        // Create criteria breakdown (simplified for JSON format)
        const criteriaBreakdown = buildCriteriaBreakdown(questionData.feedback);

        return {
            questionNumber,
            marksAwarded,
            marksPossible,
            percentage,
            feedback,
            structuredFeedback: structuredFeedback.length > 0 ? structuredFeedback : undefined,
            criteriaBreakdown,
            paragraphs: questionData.paragraphs
        };

    } catch (error) {
        console.error('Error converting JSON question to breakdown:', error);
        return null;
    }
};

const extractMarksPossibleFromJustification = (justification: string): number => {
    if (!justification) return 0;

    // Look for patterns like "X out of Y" or "X/Y" in the justification
    const patterns = [
        /(\d+)\s+out\s+of\s+(\d+)/i,
        /(\d+)\s*\/\s*(\d+)/,
        /score\s+of\s+(\d+)\s+out\s+of\s+(\d+)/i,
        /(\d+)\s+marks?\s+out\s+of\s+(\d+)/i
    ];

    for (const pattern of patterns) {
        const match = justification.match(pattern);
        if (match) {
            return sanitizeNumber(match[2], 0);
        }
    }

    // If no pattern found, return a default value
    return 0;
};

const buildComprehensiveFeedback = (feedback: QuestionFeedback): string => {
    if (!feedback) return '';

    const parts: string[] = [];

    // Add overall assessment
    if (feedback.overall_assessment) {
        parts.push(`## Overall Assessment\n\n${feedback.overall_assessment}`);
    }

    // Add paragraph analysis with new UPSC structure
    if (feedback.paragraph_by_paragraph_analysis && feedback.paragraph_by_paragraph_analysis.length > 0) {
        parts.push('\n## Detailed Analysis\n');
        feedback.paragraph_by_paragraph_analysis.forEach((analysis, index) => {
            parts.push(`### ${analysis.section_title}\n\n`);
            
            // Add core claim
            if (analysis.core_claim) {
                parts.push(`**Core Claim:** ${analysis.core_claim}\n\n`);
            }
            
            // Add evidence assessment
            if (analysis.evidence_present) {
                parts.push(`**Evidence:** ${analysis.evidence_present}\n\n`);
            }
            
            // Add reasoning quality
            if (analysis.reasoning_quality) {
                parts.push(`**Reasoning:** ${analysis.reasoning_quality}\n\n`);
            }
            
            // Add link to question
            if (analysis.link_to_question) {
                parts.push(`**Link to Question:** ${analysis.link_to_question}\n\n`);
            }
            
            // Add analysis notes
            if (analysis.analysis_notes) {
                parts.push(`**Analysis:** ${analysis.analysis_notes}\n\n`);
            }
            
            // Add specific improvements
            if (analysis.specific_improvements && analysis.specific_improvements.length > 0) {
                parts.push(`**Specific Improvements:**\n`);
                analysis.specific_improvements.forEach(improvement => {
                    parts.push(`- ${improvement}`);
                });
                parts.push('\n');
            }
            
            // Add evidence to add
            if (analysis.evidence_to_add) {
                parts.push(`**Evidence to Add:** ${analysis.evidence_to_add}\n\n`);
            }
            
            // Add structural fix
            if (analysis.structural_fix) {
                parts.push(`**Structural Fix:** ${analysis.structural_fix}\n\n`);
            }
        });
    }

    // Add positive observations
    if (feedback.positive_observations && feedback.positive_observations.length > 0) {
        parts.push('\n## Positive Observations\n');
        feedback.positive_observations.forEach(observation => {
            parts.push(`- ${observation}`);
        });
    }

    // Add critical deficiencies
    if (feedback.critical_deficiencies && feedback.critical_deficiencies.length > 0) {
        parts.push('\n## Critical Deficiencies\n');
        feedback.critical_deficiencies.forEach(deficiency => {
            parts.push(`- ${deficiency}`);
        });
    }

    // Add prescriptive actions
    if (feedback.prescriptive_actions_for_rectification) {
        // Convert numbered lists in recommendations to proper markdown format
        const formattedRecommendations = formatRecommendations(feedback.prescriptive_actions_for_rectification);
        parts.push(`\n## Recommendations\n\n${formattedRecommendations}`);
    }

    return parts.join('\n');
};

export const formatRecommendations = (recommendations: string): string => {
    if (!recommendations) return '';

    // More comprehensive pattern matching for numbered lists
    let formatted = recommendations;

    // Handle patterns like "1. ", "2. ", etc. with proper line breaks
    formatted = formatted.replace(/(\d+\.\s*)/g, '\n$1');
    
    // Handle patterns like "1) ", "2) ", etc.
    formatted = formatted.replace(/(\d+\)\s*)/g, '\n$1');
    
    // Handle patterns like "1- ", "2- ", etc.
    formatted = formatted.replace(/(\d+-\s*)/g, '\n$1');
    
    // Handle patterns like "1: ", "2: ", etc.
    formatted = formatted.replace(/(\d+:\s*)/g, '\n$1');

    // Clean up extra whitespace and ensure proper spacing
    formatted = formatted
        .replace(/\n\s*\n/g, '\n\n')  // Remove extra blank lines
        .replace(/^\s+|\s+$/g, '')    // Trim whitespace
        .replace(/\n{3,}/g, '\n\n');  // Limit consecutive line breaks to max 2

    return formatted;
};

export const formatAnalysisAndFeedback = (content: string): { analysis: string; specificImprovement: string } => {
    if (!content) {
        return { analysis: '', specificImprovement: '' };
    }

    // Look for "Specific improvement:" pattern
    const specificImprovementIndex = content.toLowerCase().indexOf('specific improvement:');
    
    if (specificImprovementIndex === -1) {
        // No specific improvement section found
        return { analysis: content, specificImprovement: '' };
    }

    // Split the content
    const analysis = content.substring(0, specificImprovementIndex).trim();
    const specificImprovement = content.substring(specificImprovementIndex).trim();

    return { analysis, specificImprovement };
};

export const formatAnalysisAndFeedbackWithLineBreaks = (content: string): string => {
    if (!content) {
        return '';
    }

    // Look for "Specific improvement:" pattern
    const specificImprovementIndex = content.toLowerCase().indexOf('specific improvement:');
    
    if (specificImprovementIndex === -1) {
        // No specific improvement section found
        return content;
    }

    // Split the content
    const analysis = content.substring(0, specificImprovementIndex).trim();
    const specificImprovement = content.substring(specificImprovementIndex).trim();

    // Format with proper line breaks and bold heading
    if (analysis && specificImprovement) {
        return `${analysis}\n\n**${specificImprovement}**`;
    } else if (analysis) {
        return analysis;
    } else if (specificImprovement) {
        return `**${specificImprovement}**`;
    }

    return content;
};

const buildStructuredFeedback = (feedback: QuestionFeedback): FeedbackSection[] => {
    const sections: FeedbackSection[] = [];

    if (!feedback) return sections;

    // Overall Assessment section
    if (feedback.overall_assessment) {
        sections.push({
            title: 'Overall Assessment',
            content: feedback.overall_assessment
        });
    }

    // Paragraph Analysis section with new UPSC structure
    if (feedback.paragraph_by_paragraph_analysis && feedback.paragraph_by_paragraph_analysis.length > 0) {
        const subsections = feedback.paragraph_by_paragraph_analysis.map(analysis => {
            // Build comprehensive content from new UPSC fields
            const contentParts: string[] = [];
            
            if (analysis.core_claim) {
                contentParts.push(`**Core Claim:** ${analysis.core_claim}`);
            }
            
            if (analysis.evidence_present) {
                contentParts.push(`**Evidence:** ${analysis.evidence_present}`);
            }
            
            if (analysis.reasoning_quality) {
                contentParts.push(`**Reasoning:** ${analysis.reasoning_quality}`);
            }
            
            if (analysis.link_to_question) {
                contentParts.push(`**Link to Question:** ${analysis.link_to_question}`);
            }
            
            if (analysis.analysis_notes) {
                contentParts.push(`**Analysis:** ${analysis.analysis_notes}`);
            }
            
            if (analysis.specific_improvements && analysis.specific_improvements.length > 0) {
                contentParts.push(`**Specific Improvements:**\n${analysis.specific_improvements.map(imp => `- ${imp}`).join('\n')}`);
            }
            
            if (analysis.evidence_to_add) {
                contentParts.push(`**Evidence to Add:** ${analysis.evidence_to_add}`);
            }
            
            if (analysis.structural_fix) {
                contentParts.push(`**Structural Fix:** ${analysis.structural_fix}`);
            }
            
            return {
                title: analysis.section_title,
                content: contentParts.join('\n\n'),
                bounding_box: analysis.bounding_box
            };
        });

        sections.push({
            title: 'Detailed Analysis',
            content: 'Paragraph-by-paragraph analysis of the answer',
            subsections
        });
    }

    // Positive Observations section
    if (feedback.positive_observations && feedback.positive_observations.length > 0) {
        sections.push({
            title: 'Positive Observations',
            content: feedback.positive_observations.map(obs => `- ${obs}`).join('\n')
        });
    }

    // Critical Deficiencies section
    if (feedback.critical_deficiencies && feedback.critical_deficiencies.length > 0) {
        sections.push({
            title: 'Critical Deficiencies',
            content: feedback.critical_deficiencies.map(def => `- ${def}`).join('\n')
        });
    }

    // Recommendations section
    if (feedback.prescriptive_actions_for_rectification) {
        // Format recommendations to handle numbered lists properly
        const formattedRecommendations = formatRecommendations(feedback.prescriptive_actions_for_rectification);
        sections.push({
            title: 'Recommendations',
            content: formattedRecommendations
        });
    }

    return sections;
};

const buildCriteriaBreakdown = (feedback: QuestionFeedback): CriterionBreakdown[] => {
    const criteria: CriterionBreakdown[] = [];

    if (!feedback || !feedback.marks_awarded) return criteria;

    // Add overall score as a criterion
    criteria.push({
        criterion: 'Overall Score',
        score: feedback.marks_awarded.score,
        maxScore: undefined // Will be filled if we can extract it
    });

    // Try to extract max score from score field (X/Y format)
    if (feedback.marks_awarded.score && feedback.marks_awarded.score.includes('/')) {
        const [, maxScore] = feedback.marks_awarded.score.split('/');
        if (maxScore) {
            criteria[0].maxScore = maxScore.trim();
        }
    }

    // Add marking rigor notes if available (new UPSC field)
    if (feedback.marks_awarded.marking_rigor_notes) {
        criteria.push({
            criterion: 'Marking Rigor Notes',
            score: feedback.marks_awarded.marking_rigor_notes,
            maxScore: undefined
        });
    }

    return criteria;
};

// --- Compatibility Functions ---
export const parseEvaluationForGradingDetails = (rawData: any): ParsedEvaluation | null => {
    try {
        const evaluationBreakdown = parseJsonEvaluationData(rawData);
        if (!evaluationBreakdown) {
            return null;
        }

        // Convert to GradingDetails format
        return convertToGradingDetailsFormat(evaluationBreakdown);

    } catch (error) {
        console.error('Error parsing evaluation for grading details:', error);
        return null;
    }
};

const convertToGradingDetailsFormat = (evaluationBreakdown: EvaluationBreakdown): ParsedEvaluation => {
    // Create sections from questions
    const sections: ParsedEvaluation['section'] = [];

    if (evaluationBreakdown.questions.length > 0) {
        // Group questions into sections (for now, create one main section)
        const questions = evaluationBreakdown.questions.map(q => ({
            question_number: parseInt(q.questionNumber.replace(/\D/g, '')) || 1,
            marks_awarded: q.marksAwarded,
            marks_possible: q.marksPossible,
            feedback: q.feedback
        }));

        sections.push({
            name: 'Main Section',
            section_marks: evaluationBreakdown.totalMarks,
            section_possible_marks: evaluationBreakdown.maxMarks,
            question: questions
        });
    }

    return {
        total_marks: evaluationBreakdown.totalMarks,
        maximum_possible_marks: evaluationBreakdown.maxMarks,
        percentage_score: evaluationBreakdown.overallPercentage,
        section: sections
    };
};

// --- Validation Functions ---
export const validateJsonEvaluationData = (data: any): { isValid: boolean; errors: string[]; warnings: string[] } => {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
        if (!data) {
            errors.push('No evaluation data provided');
            return { isValid: false, errors, warnings };
        }

        const parsed = parseJsonEvaluationData(data);
        if (!parsed) {
            errors.push('Failed to parse evaluation data');
            return { isValid: false, errors, warnings };
        }

        if (parsed.questions.length === 0) {
            warnings.push('No questions found in evaluation data');
        }

        if (parsed.totalMarks === 0 && parsed.maxMarks === 0) {
            warnings.push('No marks information available');
        }

        parsed.questions.forEach((question, index) => {
            if (!question.questionNumber) {
                warnings.push(`Question ${index + 1} has no number`);
            }
            if (question.marksPossible === 0) {
                warnings.push(`Question ${question.questionNumber || index + 1} has no possible marks`);
            }
            if (!question.feedback) {
                warnings.push(`Question ${question.questionNumber || index + 1} has no feedback`);
            }
        });

        return { isValid: true, errors, warnings };
    } catch (error) {
        errors.push(`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        return { isValid: false, errors, warnings };
    }
};

// --- Calculation Helper Functions ---
export const calculateTotalMarksFromQuestions = (questions: QuestionBreakdown[]): number => {
    if (!questions || questions.length === 0) {
        return 0;
    }

    return questions.reduce((total, question) => {
        const questionMarks = sanitizeNumber(question.marksAwarded, 0);
        return total + questionMarks;
    }, 0);
};

export const calculateTotalPossibleMarksFromQuestions = (questions: QuestionBreakdown[]): number => {
    if (!questions || questions.length === 0) {
        return 0;
    }

    return questions.reduce((total, question) => {
        const questionPossibleMarks = sanitizeNumber(question.marksPossible, 0);
        return total + questionPossibleMarks;
    }, 0);
};

export const validateAndCorrectEvaluationTotals = (evaluationData: EvaluationBreakdown): EvaluationBreakdown => {
    if (!evaluationData || !evaluationData.questions) {
        return evaluationData;
    }

    const calculatedTotalMarks = calculateTotalMarksFromQuestions(evaluationData.questions);
    const calculatedMaxMarks = calculateTotalPossibleMarksFromQuestions(evaluationData.questions);
    const calculatedPercentage = sanitizePercentage(calculatedTotalMarks, calculatedMaxMarks);

    // Check if there's a discrepancy and log it
    if (evaluationData.totalMarks !== calculatedTotalMarks) {
        console.warn(`Total marks mismatch detected:`, {
            originalTotal: evaluationData.totalMarks,
            calculatedTotal: calculatedTotalMarks,
            difference: Math.abs(evaluationData.totalMarks - calculatedTotalMarks)
        });
    }

    if (evaluationData.maxMarks !== calculatedMaxMarks) {
        console.warn(`Max marks mismatch detected:`, {
            originalMax: evaluationData.maxMarks,
            calculatedMax: calculatedMaxMarks,
            difference: Math.abs(evaluationData.maxMarks - calculatedMaxMarks)
        });
    }

    // Return corrected evaluation data
    return {
        ...evaluationData,
        totalMarks: calculatedTotalMarks,
        maxMarks: calculatedMaxMarks,
        overallPercentage: calculatedPercentage
    };
};

// --- Legacy Support Functions ---
export const parseQuestionBreakdown = parseJsonEvaluationData;

// Export utility functions for external use
export {
    sanitizeNumber,
    sanitizeString,
    sanitizePercentage,
    extractJsonFromRawData,
    cleanAndValidateJson
}; 