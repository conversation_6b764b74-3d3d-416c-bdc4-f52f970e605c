/**
 * Utility functions for converting markdown content to PDF-friendly plain text
 * Used in the annotated PDF generation process
 */

export interface MarkdownConversionOptions {
  maxLength?: number;
  preserveLineBreaks?: boolean;
  removeCodeBlocks?: boolean;
  simplifyLists?: boolean;
}

/**
 * Convert markdown text to plain text suitable for PDF rendering
 * @param markdown - The markdown content to convert
 * @param options - Conversion options
 * @returns Plain text suitable for PDF rendering
 */
export const markdownToPlainText = (
  markdown: string, 
  options: MarkdownConversionOptions = {}
): string => {
  if (!markdown || typeof markdown !== 'string') return '';
  
  const {
    maxLength = 1000,
    preserveLineBreaks = false,
    removeCodeBlocks = true,
    simplifyLists = true
  } = options;
  
  let text = markdown;
  
  // Remove markdown headers (# ## ###)
  text = text.replace(/^#{1,6}\s+/gm, '');
  
  // Remove bold/italic markers but keep the text
  text = text.replace(/\*\*(.*?)\*\*/g, '$1'); // **bold**
  text = text.replace(/\*(.*?)\*/g, '$1');     // *italic*
  text = text.replace(/__(.*?)__/g, '$1');     // __bold__
  text = text.replace(/_(.*?)_/g, '$1');       // _italic_
  
  // Remove code blocks if requested
  if (removeCodeBlocks) {
    text = text.replace(/```[\s\S]*?```/g, ''); // Code blocks
    text = text.replace(/`([^`]+)`/g, '$1');    // Inline code
  }
  
  // Remove links but keep the text
  text = text.replace(/\[([^\]]+)\]\([^)]+\)/g, '$1');
  
  // Simplify lists if requested
  if (simplifyLists) {
    text = text.replace(/^\s*[-*+]\s+/gm, '• '); // Unordered lists
    text = text.replace(/^\s*\d+\.\s+/gm, '');   // Ordered lists (remove numbers)
  }
  
  // Handle line breaks
  if (preserveLineBreaks) {
    text = text.replace(/\n\s*\n/g, '\n'); // Single line breaks
  } else {
    text = text.replace(/\n\s*\n/g, ' ');   // Convert to spaces
    text = text.replace(/\s+/g, ' ');       // Normalize whitespace
  }
  
  // Clean up and trim
  text = text.trim();
  
  // Truncate if too long
  if (maxLength && text.length > maxLength) {
    text = text.substring(0, maxLength - 3) + '...';
  }
  
  return text;
};

/**
 * Split long text into chunks suitable for PDF rendering
 * @param text - The text to split
 * @param maxChunkLength - Maximum length per chunk
 * @returns Array of text chunks
 */
export const splitTextIntoChunks = (text: string, maxChunkLength: number = 200): string[] => {
  if (!text || text.length <= maxChunkLength) {
    return [text];
  }
  
  const chunks: string[] = [];
  let currentChunk = '';
  
  // Split by sentences first
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
  
  for (const sentence of sentences) {
    const trimmedSentence = sentence.trim();
    if (trimmedSentence.length === 0) continue;
    
    const sentenceWithPeriod = trimmedSentence + '.';
    
    if ((currentChunk + ' ' + sentenceWithPeriod).length <= maxChunkLength) {
      currentChunk += (currentChunk ? ' ' : '') + sentenceWithPeriod;
    } else {
      if (currentChunk) {
        chunks.push(currentChunk.trim());
      }
      
      // If single sentence is too long, split by words
      if (sentenceWithPeriod.length > maxChunkLength) {
        const words = sentenceWithPeriod.split(' ');
        let wordChunk = '';
        
        for (const word of words) {
          if ((wordChunk + ' ' + word).length <= maxChunkLength) {
            wordChunk += (wordChunk ? ' ' : '') + word;
          } else {
            if (wordChunk) {
              chunks.push(wordChunk.trim());
            }
            wordChunk = word;
          }
        }
        
        if (wordChunk) {
          currentChunk = wordChunk;
        } else {
          currentChunk = '';
        }
      } else {
        currentChunk = sentenceWithPeriod;
      }
    }
  }
  
  if (currentChunk) {
    chunks.push(currentChunk.trim());
  }
  
  return chunks.length > 0 ? chunks : [text];
};

/**
 * Extract key points from markdown content for summary display
 * @param markdown - The markdown content
 * @param maxPoints - Maximum number of points to extract
 * @returns Array of key points
 */
export const extractKeyPoints = (markdown: string, maxPoints: number = 5): string[] => {
  if (!markdown) return [];
  
  const points: string[] = [];
  
  // Extract list items
  const listItems = markdown.match(/^\s*[-*+]\s+(.+)$/gm);
  if (listItems) {
    listItems.forEach(item => {
      const cleanItem = item.replace(/^\s*[-*+]\s+/, '').trim();
      if (cleanItem && points.length < maxPoints) {
        points.push(markdownToPlainText(cleanItem, { maxLength: 100 }));
      }
    });
  }
  
  // If not enough points from lists, extract from sentences
  if (points.length < maxPoints) {
    const sentences = markdown.split(/[.!?]+/).filter(s => s.trim().length > 20);
    for (const sentence of sentences) {
      if (points.length >= maxPoints) break;
      const cleanSentence = markdownToPlainText(sentence.trim(), { maxLength: 100 });
      if (cleanSentence && !points.includes(cleanSentence)) {
        points.push(cleanSentence);
      }
    }
  }
  
  return points.slice(0, maxPoints);
};

/**
 * Test function to preview markdown conversion
 * @param markdown - The markdown to test
 * @returns Object with different conversion results
 */
export const previewMarkdownConversion = (markdown: string) => {
  return {
    original: markdown,
    plainText: markdownToPlainText(markdown),
    chunks: splitTextIntoChunks(markdownToPlainText(markdown), 150),
    keyPoints: extractKeyPoints(markdown),
    shortSummary: markdownToPlainText(markdown, { maxLength: 200 })
  };
};
